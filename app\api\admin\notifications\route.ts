import { NextRequest } from 'next/server'
import { z } from 'zod'
import { createAPIHandler, APIResponse, rateLimits } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const createNotificationSchema = z.object({
  type: z.enum([
    'QUIZ_AVAILABLE',
    'QUIZ_REMINDER', 
    'QUIZ_STARTING',
    'QUIZ_ENDING',
    'RESULT_PUBLISHED',
    'ACHIEVEMENT_UNLOCKED',
    'SYSTEM_ANNOUNCEMENT',
    'MAINTENANCE_NOTICE',
    'WELCOME_MESSAGE',
    'CUSTOM'
  ]),
  title: z.string().min(1, "Title is required"),
  message: z.string().min(1, "Message is required"),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).default('normal'),
  category: z.string().optional(),
  actionUrl: z.string().url().optional(),
  imageUrl: z.string().url().optional(),
  expiresAt: z.string().datetime().optional(),
  scheduledAt: z.string().datetime().optional(),
  targetUsers: z.array(z.string()).optional(), // User IDs to send to
  targetRoles: z.array(z.enum(['STUDENT', 'ADMIN'])).optional(),
  sendToAll: z.boolean().default(false)
})

const querySchema = z.object({
  page: z.string().transform(val => parseInt(val) || 1),
  limit: z.string().transform(val => Math.min(parseInt(val) || 10, 50)),
  type: z.string().optional(),
  priority: z.string().optional(),
  status: z.enum(['all', 'sent', 'scheduled', 'draft']).optional().default('all'),
  search: z.string().optional()
})

// GET /api/admin/notifications - Get notifications with pagination and filtering
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.moderate,
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    const { page, limit, type, priority, status, search } = validatedQuery
    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { message: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (type) {
      where.type = type
    }

    if (priority) {
      where.priority = priority
    }

    // Filter by status
    if (status !== 'all') {
      switch (status) {
        case 'sent':
          where.sentAt = { not: null }
          break
        case 'scheduled':
          where.scheduledAt = { not: null }
          where.sentAt = null
          break
        case 'draft':
          where.sentAt = null
          where.scheduledAt = null
          break
      }
    }

    const [notifications, totalCount] = await Promise.all([
      prisma.notification.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          userNotifications: {
            select: {
              id: true,
              isRead: true,
              isClicked: true,
              deliveredAt: true
            }
          }
        }
      }),
      prisma.notification.count({ where })
    ])

    // Process notifications with stats
    const notificationsWithStats = notifications.map(notification => {
      const recipientCount = notification.userNotifications.length
      const deliveredCount = notification.userNotifications.filter(un => un.deliveredAt).length
      const readCount = notification.userNotifications.filter(un => un.isRead).length
      const clickCount = notification.userNotifications.filter(un => un.isClicked).length

      return {
        id: notification.id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        priority: notification.priority,
        category: notification.category,
        actionUrl: notification.actionUrl,
        imageUrl: notification.imageUrl,
        expiresAt: notification.expiresAt?.toISOString(),
        scheduledAt: notification.scheduledAt?.toISOString(),
        sentAt: notification.sentAt?.toISOString(),
        createdAt: notification.createdAt.toISOString(),
        updatedAt: notification.updatedAt.toISOString(),
        recipientCount,
        deliveredCount,
        readCount,
        clickCount,
        deliveryRate: recipientCount > 0 ? Math.round((deliveredCount / recipientCount) * 100) : 0,
        readRate: deliveredCount > 0 ? Math.round((readCount / deliveredCount) * 100) : 0,
        clickRate: readCount > 0 ? Math.round((clickCount / readCount) * 100) : 0,
        status: notification.sentAt ? 'sent' : notification.scheduledAt ? 'scheduled' : 'draft'
      }
    })

    const totalPages = Math.ceil(totalCount / limit)

    return APIResponse.success({
      notifications: notificationsWithStats,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }, 'Notifications retrieved successfully')
  }
)

// POST /api/admin/notifications - Create and optionally send notification
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.strict,
    validateBody: createNotificationSchema
  },
  async (request: NextRequest, { validatedBody }) => {
    const {
      type,
      title,
      message,
      priority,
      category,
      actionUrl,
      imageUrl,
      expiresAt,
      scheduledAt,
      targetUsers,
      targetRoles,
      sendToAll
    } = validatedBody

    try {
      // Create the notification
      const notification = await prisma.notification.create({
        data: {
          type,
          title,
          message,
          priority,
          category,
          actionUrl,
          imageUrl,
          expiresAt: expiresAt ? new Date(expiresAt) : null,
          scheduledAt: scheduledAt ? new Date(scheduledAt) : null,
          sentAt: scheduledAt ? null : new Date() // Send immediately if not scheduled
        }
      })

      // Determine target users
      let userIds: string[] = []

      if (sendToAll) {
        const allUsers = await prisma.user.findMany({
          select: { id: true }
        })
        userIds = allUsers.map(user => user.id)
      } else {
        if (targetUsers && targetUsers.length > 0) {
          userIds.push(...targetUsers)
        }
        
        if (targetRoles && targetRoles.length > 0) {
          const roleUsers = await prisma.user.findMany({
            where: {
              role: { in: targetRoles }
            },
            select: { id: true }
          })
          userIds.push(...roleUsers.map(user => user.id))
        }
      }

      // Remove duplicates
      userIds = [...new Set(userIds)]

      // Create user notifications
      if (userIds.length > 0) {
        const userNotifications = userIds.map(userId => ({
          userId,
          notificationId: notification.id,
          deliveredAt: scheduledAt ? null : new Date() // Deliver immediately if not scheduled
        }))

        await prisma.userNotification.createMany({
          data: userNotifications
        })
      }

      return APIResponse.success(
        {
          id: notification.id,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          priority: notification.priority,
          category: notification.category,
          actionUrl: notification.actionUrl,
          imageUrl: notification.imageUrl,
          expiresAt: notification.expiresAt?.toISOString(),
          scheduledAt: notification.scheduledAt?.toISOString(),
          sentAt: notification.sentAt?.toISOString(),
          createdAt: notification.createdAt.toISOString(),
          recipientCount: userIds.length,
          status: notification.sentAt ? 'sent' : notification.scheduledAt ? 'scheduled' : 'draft'
        },
        `Notification ${notification.sentAt ? 'sent' : 'created'} successfully`,
        201
      )
    } catch (error) {
      console.error('Error creating notification:', error)
      return APIResponse.error('Failed to create notification', 500)
    }
  }
)
