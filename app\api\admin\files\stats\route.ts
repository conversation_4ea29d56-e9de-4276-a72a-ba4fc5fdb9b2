import { NextRequest } from 'next/server'
import { z } from 'zod'
import { createAPIHandler, APIResponse, rateLimits } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const querySchema = z.object({
  period: z.enum(['7d', '30d', '90d', 'all']).optional().default('30d')
})

// GET /api/admin/files/stats - Get file management statistics
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.moderate,
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    const { period } = validatedQuery

    // Calculate date range
    let dateFilter: any = {}
    if (period !== 'all') {
      const days = period === '7d' ? 7 : period === '30d' ? 30 : 90
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)
      dateFilter = {
        createdAt: {
          gte: startDate
        }
      }
    }

    try {
   

      // Get total file counts
      const [totalFiles, totalSize] = await Promise.all([
        prisma.file.count({
          where: dateFilter
        }),
        prisma.file.aggregate({
          where: dateFilter,
          _sum: {
            size: true
          }
        })
      ])

      // Get files by type
      const filesByType = await prisma.file.groupBy({
        by: ['uploadType'],
        where: dateFilter,
        _count: {
          id: true
        },
        _sum: {
          size: true
        }
      }).catch(error => {
        console.error('Error in filesByType query:', error)
        return []
      })

      // Get files by folder
      const filesByFolder = await prisma.file.groupBy({
        by: ['folder'],
        where: dateFilter,
        _count: {
          id: true
        },
        _sum: {
          size: true
        }
      }).catch(error => {
        console.error('Error in filesByFolder query:', error)
        return []
      })

      // Get recent uploads
      const recentUploads = await prisma.file.findMany({
        where: dateFilter,
        take: 10,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          uploadedBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      }).catch(error => {
        console.error('Error in recentUploads query:', error)
        return []
      })

      // Get daily upload counts for the period (for charts)
      // Using Prisma queries instead of raw SQL for better compatibility
      const recentFiles = await prisma.file.findMany({
        where: dateFilter,
        select: {
          createdAt: true,
          size: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      }).catch(error => {
        console.error('Error in recentFiles query:', error)
        return []
      })

      // Group by date manually
      const dailyStatsMap = new Map<string, { count: number; totalSize: number }>()
      recentFiles.forEach(file => {
        const dateKey = file.createdAt.toISOString().split('T')[0] // YYYY-MM-DD
        const existing = dailyStatsMap.get(dateKey) || { count: 0, totalSize: 0 }
        dailyStatsMap.set(dateKey, {
          count: existing.count + 1,
          totalSize: existing.totalSize + file.size
        })
      })

      // Convert to array and sort
      const dailyUploads = Array.from(dailyStatsMap.entries())
        .map(([date, stats]) => ({
          date,
          count: stats.count,
          total_size: stats.totalSize
        }))
        .sort((a, b) => b.date.localeCompare(a.date))
        .slice(0, 30)

      // Get top uploaders
      const topUploaders = await prisma.file.groupBy({
        by: ['uploadedById'],
        where: dateFilter,
        _count: {
          id: true
        },
        _sum: {
          size: true
        },
        orderBy: {
          _count: {
            id: 'desc'
          }
        },
        take: 5
      }).catch(error => {
        console.error('Error in topUploaders query:', error)
        return []
      })

      // Get user details for top uploaders
      const uploaderIds = topUploaders.map(u => u.uploadedById)
      const uploaderDetails = uploaderIds.length > 0 ? await prisma.user.findMany({
        where: {
          id: { in: uploaderIds }
        },
        select: {
          id: true,
          name: true,
          email: true
        }
      }).catch(error => {
        console.error('Error in uploaderDetails query:', error)
        return []
      }) : []

      // Format file size
      const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 B'
        const k = 1024
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
      }

      const totalSizeBytes = totalSize._sum.size || 0

      const stats = {
        overview: {
          totalFiles,
          totalSize: formatFileSize(totalSizeBytes),
          totalSizeBytes,
          averageFileSize: totalFiles > 0 ? formatFileSize(Math.round(totalSizeBytes / totalFiles)) : '0 B',
          averageFileSizeBytes: totalFiles > 0 ? Math.round(totalSizeBytes / totalFiles) : 0
        },
        filesByType: filesByType.map(item => ({
          type: item.uploadType,
          count: item._count.id,
          size: formatFileSize(item._sum.size || 0),
          sizeBytes: item._sum.size || 0
        })),
        filesByFolder: filesByFolder.map(item => ({
          folder: item.folder,
          count: item._count.id,
          size: formatFileSize(item._sum.size || 0),
          sizeBytes: item._sum.size || 0
        })),
        recentUploads: recentUploads.map(file => ({
          id: file.id,
          filename: file.filename,
          originalName: file.originalName,
          mimeType: file.mimeType,
          size: file.size,
          formattedSize: formatFileSize(file.size),
          uploadType: file.uploadType,
          folder: file.folder,
          createdAt: file.createdAt.toISOString(),
          uploadedBy: file.uploadedBy,
          isImage: file.mimeType.startsWith('image/'),
          isDocument: file.mimeType.includes('pdf') || file.mimeType.includes('document') || file.mimeType.includes('text')
        })),
        topUploaders: topUploaders.map(uploader => {
          const userDetails = uploaderDetails.find(u => u.id === uploader.uploadedById)
          return {
            user: userDetails,
            fileCount: uploader._count.id,
            totalSize: formatFileSize(uploader._sum.size || 0),
            totalSizeBytes: uploader._sum.size || 0
          }
        }),
        dailyUploads: dailyUploads.map(day => ({
          date: day.date,
          count: Number(day.count),
          totalSize: Number(day.total_size || 0),
          formattedSize: formatFileSize(Number(day.total_size || 0))
        })),
        period
      }

      
      return APIResponse.success(stats, 'File statistics retrieved successfully')
    } catch (error) {
      console.error('Error fetching file stats:', error)
      return APIResponse.error('Failed to fetch file statistics', 500)
    }
  }
)
