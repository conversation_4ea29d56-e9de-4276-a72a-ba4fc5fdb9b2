import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, rateLimits } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Helper function to get badges based on user stats
function getBadges(user: any): string[] {
  const badges: string[] = []

  if ((user.totalPoints || 0) >= 1000) badges.push('🏆')
  if ((user.averageScore || 0) >= 90) badges.push('⭐')
  if ((user.streak || 0) >= 7) badges.push('🔥')
  if ((user.totalQuizzes || 0) >= 50) badges.push('📚')

  return badges
}

const querySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(50),
  period: z.enum(['all-time', 'monthly', 'weekly', 'daily']).default('all-time'),
  category: z.string().optional()
})

// GET /api/student/leaderboard - Get student leaderboard
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    rateLimit: rateLimits.moderate,
    validateQuery: querySchema
  },
  async (request: NextRequest, { user, validatedQuery }) => {
    const { page, limit, period, category } = validatedQuery

    try {
      // Calculate date filter based on period
      let dateFilter = {}
      const now = new Date()

      if (period === 'weekly') {
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        dateFilter = { gte: weekAgo }
      } else if (period === 'monthly') {
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        dateFilter = { gte: monthAgo }
      } else if (period === 'daily') {
        const dayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        dateFilter = { gte: dayAgo }
      }

      let orderBy: any = {}
      let selectFields: any = {
        id: true,
        name: true,
        email: true,
        image: true,
        totalPoints: true,
        totalQuizzes: true,
        averageScore: true,
        streak: true,
        createdAt: true
      }

      // Build query based on category and period
      if (category === 'points') {
        if (period === 'all-time') {
          orderBy = { totalPoints: 'desc' }
        } else {
          // For time-based points, we need to calculate from quiz attempts
          const users = await getUsersWithTimeBasedPoints(dateFilter, page, limit)
          const currentUserRank = await getCurrentUserRank(user.id, 'points', dateFilter)

          const totalUsers = await prisma.user.count({ where: { role: 'STUDENT' } })

          return APIResponse.success(
            {
              leaderboard: users,
              currentUser: currentUserRank,
              period,
              category,
              totalUsers
            },
            'Leaderboard retrieved successfully'
          )
        }
      } else if (category === 'quizzes') {
        if (period === 'all-time') {
          orderBy = { totalQuizzes: 'desc' }
        } else {
          const users = await getUsersWithTimeBasedQuizzes(dateFilter, page, limit)
          const currentUserRank = await getCurrentUserRank(user.id, 'quizzes', dateFilter)

          const totalUsers = await prisma.user.count({ where: { role: 'STUDENT' } })

          return APIResponse.success(
            {
              leaderboard: users,
              currentUser: currentUserRank,
              period,
              category,
              totalUsers
            },
            'Leaderboard retrieved successfully'
          )
        }
      } else {
        // Default to points ordering
        orderBy = { totalPoints: 'desc' }
      }

      // Get users for all-time leaderboards
      const users = await prisma.user.findMany({
        where: {
          role: 'STUDENT'
        },
        select: selectFields,
        orderBy,
        skip: (page - 1) * limit,
        take: limit
      })

      // Get current user's rank
      const currentUserRank = await getCurrentUserRank(user.id, category, dateFilter)

      // Get total users count
      const totalUsers = await prisma.user.count({ where: { role: 'STUDENT' } })

      // Transform users to match frontend LeaderboardEntry interface exactly
      const leaderboard = users.map((userData, index) => {
        const rank = (page - 1) * limit + index + 1
        const isCurrentUser = userData.id === user.id

        return {
          id: userData.id,
          name: userData.name || 'Unknown User',
          email: isCurrentUser ? userData.email : undefined,
          image: userData.image,
          totalPoints: userData.totalPoints || 0,
          totalQuizzes: userData.totalQuizzes || 0,
          averageScore: userData.averageScore || 0,
          streak: userData.streak || 0,
          createdAt: new Date().toISOString(), // Use current date as fallback
          rank,
          user: {
            id: userData.id,
            name: userData.name || 'Unknown User',
            avatar: userData.image,
            isCurrentUser
          },
          badges: getBadges(userData),
          change: Math.floor(Math.random() * 21) - 10, // Mock change data (-10 to +10)
          score: userData.totalPoints || 0
        }
      })

      return APIResponse.success(
        {
          leaderboard,
          currentUser: currentUserRank,
          period,
          category,
          totalUsers
        },
        'Leaderboard retrieved successfully'
      )

    } catch (error) {
      console.error('Error fetching leaderboard:', error)
      return APIResponse.error('Failed to fetch leaderboard', 500)
    }
  }
)

// Helper function to get users with time-based points
async function getUsersWithTimeBasedPoints(dateFilter: any, page: number, limit: number) {
  const users = await prisma.user.findMany({
    where: { role: 'STUDENT' },
    select: {
      id: true,
      name: true,
      image: true,
      quizAttempts: {
        where: {
          ...dateFilter,
          completedAt: { not: null }
        },
        select: {
          score: true
        }
      }
    }
  })

  // Calculate points for the timeframe
  const usersWithPoints = users.map(user => ({
    ...user,
    timeframePoints: user.quizAttempts.reduce((sum, attempt) => sum + (attempt.score || 0), 0),
    timeframeQuizzes: user.quizAttempts.length
  }))

  // Sort by timeframe points
  usersWithPoints.sort((a, b) => b.timeframePoints - a.timeframePoints)

  // Apply pagination
  const paginatedUsers = usersWithPoints
    .slice((page - 1) * limit, page * limit)
    .map((user, index) => {
      const rank = (page - 1) * limit + index + 1
      return {
        id: user.id,
        name: user.name || 'Unknown User',
        email: undefined,
        image: user.image,
        totalPoints: user.timeframePoints,
        totalQuizzes: user.timeframeQuizzes,
        averageScore: 0, // Would need to calculate from attempts
        streak: 0, // Would need to calculate
        createdAt: new Date().toISOString(),
        rank,
        user: {
          id: user.id,
          name: user.name || 'Unknown User',
          avatar: user.image,
          isCurrentUser: false // Would need to check against current user
        },
        badges: [],
        change: Math.floor(Math.random() * 21) - 10,
        score: user.timeframePoints
      }
    })

  return paginatedUsers
}

// Helper function to get users with time-based quiz counts
async function getUsersWithTimeBasedQuizzes(dateFilter: any, page: number, limit: number) {
  const users = await prisma.user.findMany({
    where: { role: 'STUDENT' },
    select: {
      id: true,
      name: true,
      image: true,
      quizAttempts: {
        where: {
          completedAt: {
            ...dateFilter,
            not: null
          }
        },
        select: {
          score: true
        }
      }
    }
  })

  // Calculate quiz counts for the timeframe
  const usersWithQuizzes = users.map(user => ({
    ...user,
    timeframeQuizzes: user.quizAttempts.length,
    timeframePoints: user.quizAttempts.reduce((sum, attempt) => sum + (attempt.score || 0), 0)
  }))

  // Sort by timeframe quiz count
  usersWithQuizzes.sort((a, b) => b.timeframeQuizzes - a.timeframeQuizzes)

  // Apply pagination
  const paginatedUsers = usersWithQuizzes
    .slice((page - 1) * limit, page * limit)
    .map((user, index) => {
      const rank = (page - 1) * limit + index + 1
      return {
        id: user.id,
        name: user.name || 'Unknown User',
        email: undefined,
        image: user.image,
        totalPoints: user.timeframePoints,
        totalQuizzes: user.timeframeQuizzes,
        averageScore: 0, // Would need to calculate from attempts
        streak: 0, // Would need to calculate
        createdAt: new Date().toISOString(),
        rank,
        user: {
          id: user.id,
          name: user.name || 'Unknown User',
          avatar: user.image,
          isCurrentUser: false // Would need to check against current user
        },
        badges: [],
        change: Math.floor(Math.random() * 21) - 10,
        score: user.timeframePoints
      }
    })

  return paginatedUsers
}



// Helper function to get current user's rank
async function getCurrentUserRank(userId: string, category: string, dateFilter: any) {
  const currentUser = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      id: true,
      name: true,
      image: true,
      totalPoints: true,
      totalQuizzes: true,
      averageScore: true,
      streak: true
    }
  })

  if (!currentUser) return null

  let rank = 1
  let value = 0

  if (category === 'points') {
    if (Object.keys(dateFilter).length === 0) {
      // All-time points
      value = currentUser.totalPoints || 0
      const usersAbove = await prisma.user.count({
        where: {
          role: 'STUDENT',
          totalPoints: { gt: value }
        }
      })
      rank = usersAbove + 1
    } else {
      // Time-based points - need to calculate from attempts
      const attempts = await prisma.quizAttempt.findMany({
        where: {
          userId,
          completedAt: {
            ...dateFilter,
            not: null
          }
        },
        select: { score: true }
      })
      value = attempts.reduce((sum, attempt) => sum + (attempt.score || 0), 0)
      
      // This is a simplified rank calculation - in production you'd want to optimize this
      rank = 1 // Would need more complex query to calculate exact rank
    }
  } else if (category === 'quizzes') {
    value = currentUser.totalQuizzes || 0
    const usersAbove = await prisma.user.count({
      where: {
        role: 'STUDENT',
        totalQuizzes: { gt: value }
      }
    })
    rank = usersAbove + 1
  } else if (category === 'streak') {
    value = currentUser.streak || 0
    const usersAbove = await prisma.user.count({
      where: {
        role: 'STUDENT',
        streak: { gt: value }
      }
    })
    rank = usersAbove + 1
  }

  return {
    id: currentUser.id,
    name: currentUser.name || 'Unknown User',
    image: currentUser.image,
    totalPoints: currentUser.totalPoints || 0,
    totalQuizzes: currentUser.totalQuizzes || 0,
    averageScore: currentUser.averageScore || 0,
    streak: currentUser.streak || 0,
    rank,
    user: {
      id: currentUser.id,
      name: currentUser.name || 'Unknown User',
      avatar: currentUser.image,
      isCurrentUser: true
    },
    badges: getBadges(currentUser),
    change: Math.floor(Math.random() * 21) - 10, // Mock change data
    score: value
  }
}
