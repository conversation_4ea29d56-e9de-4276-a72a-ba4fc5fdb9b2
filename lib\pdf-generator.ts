import jsPDF from 'jspdf'
import 'jspdf-autotable'
import html2canvas from 'html2canvas'

// Extend jsPDF type to include autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF
    lastAutoTable: { finalY: number }
  }
}

interface PDFOptions {
  title?: string
  author?: string
  subject?: string
  keywords?: string
  creator?: string
  orientation?: 'portrait' | 'landscape'
  unit?: 'mm' | 'pt' | 'in'
  format?: string | number[]
}

interface QuizResult {
  id: string
  quiz: {
    title: string
    description: string
    category: string
    difficulty: string
    totalQuestions: number
    totalPoints: number
    duration: number
  }
  student: {
    name: string
    email: string
    id: string
  }
  score: number
  percentage: number
  timeSpent: number
  completedAt: string
  questions: Array<{
    id: string
    text: string
    type: string
    options?: string[]
    correctAnswer: string
    studentAnswer: any
    isCorrect: boolean
    points: number
    pointsEarned: number
    explanation?: string
  }>
  rank?: number
  totalAttempts?: number
}

interface AnalyticsData {
  overview: {
    totalAttempts: number
    averageScore: number
    totalTimeSpent: number
    improvementRate: number
  }
  performanceByCategory: Array<{
    category: string
    attempts: number
    averageScore: number
    improvement: number
  }>
  performanceByDifficulty: Array<{
    difficulty: string
    attempts: number
    averageScore: number
    successRate: number
  }>
  weeklyProgress: Array<{
    week: string
    attempts: number
    averageScore: number
    timeSpent: number
  }>
}

class PDFGenerator {
  private doc: jsPDF
  private pageWidth: number
  private pageHeight: number
  private margin: number = 20

  constructor(options: PDFOptions = {}) {
    this.doc = new jsPDF({
      orientation: options.orientation || 'portrait',
      unit: options.unit || 'mm',
      format: options.format || 'a4'
    })

    this.pageWidth = this.doc.internal.pageSize.getWidth()
    this.pageHeight = this.doc.internal.pageSize.getHeight()

    // Set document properties
    if (options.title) this.doc.setProperties({ title: options.title })
    if (options.author) this.doc.setProperties({ author: options.author })
    if (options.subject) this.doc.setProperties({ subject: options.subject })
    if (options.keywords) this.doc.setProperties({ keywords: options.keywords })
    if (options.creator) this.doc.setProperties({ creator: options.creator })
  }

  // Add header with logo and title
  private addHeader(title: string, subtitle?: string) {
    const headerHeight = 30
    
    // Background for header
    this.doc.setFillColor(59, 130, 246) // Blue background
    this.doc.rect(0, 0, this.pageWidth, headerHeight, 'F')
    
    // Title
    this.doc.setTextColor(255, 255, 255)
    this.doc.setFontSize(20)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text(title, this.margin, 20)
    
    if (subtitle) {
      this.doc.setFontSize(12)
      this.doc.setFont('helvetica', 'normal')
      this.doc.text(subtitle, this.margin, 26)
    }
    
    // Reset text color
    this.doc.setTextColor(0, 0, 0)
    
    return headerHeight + 10
  }

  // Add footer with page numbers and timestamp
  private addFooter() {
    const footerY = this.pageHeight - 15
    
    this.doc.setFontSize(8)
    this.doc.setTextColor(128, 128, 128)
    
    // Page number
    const pageNum = this.doc.getCurrentPageInfo().pageNumber
    this.doc.text(`Page ${pageNum}`, this.pageWidth - this.margin - 20, footerY)
    
    // Timestamp
    const timestamp = new Date().toLocaleString()
    this.doc.text(`Generated on ${timestamp}`, this.margin, footerY)
  }

  // Generate Quiz Result PDF
  async generateQuizResultPDF(result: QuizResult): Promise<Blob> {
    // Header
    let currentY = this.addHeader(
      'Quiz Result Report',
      `${result.quiz.title} - ${result.student.name}`
    )

    // Student Information Section
    this.doc.setFontSize(16)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('Student Information', this.margin, currentY)
    currentY += 10

    const studentInfo = [
      ['Name', result.student.name],
      ['Email', result.student.email],
      ['Completed At', new Date(result.completedAt).toLocaleString()],
      ['Time Spent', `${result.timeSpent} minutes`]
    ]

    this.doc.autoTable({
      startY: currentY,
      head: [['Field', 'Value']],
      body: studentInfo,
      theme: 'grid',
      headStyles: { fillColor: [59, 130, 246] },
      margin: { left: this.margin, right: this.margin }
    })

    currentY = this.doc.lastAutoTable.finalY + 15

    // Quiz Information Section
    this.doc.setFontSize(16)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('Quiz Information', this.margin, currentY)
    currentY += 10

    const quizInfo = [
      ['Title', result.quiz.title],
      ['Category', result.quiz.category],
      ['Difficulty', result.quiz.difficulty],
      ['Total Questions', result.quiz.totalQuestions.toString()],
      ['Total Points', result.quiz.totalPoints.toString()],
      ['Duration', `${result.quiz.duration} minutes`]
    ]

    this.doc.autoTable({
      startY: currentY,
      head: [['Field', 'Value']],
      body: quizInfo,
      theme: 'grid',
      headStyles: { fillColor: [59, 130, 246] },
      margin: { left: this.margin, right: this.margin }
    })

    currentY = this.doc.lastAutoTable.finalY + 15

    // Performance Summary
    this.doc.setFontSize(16)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('Performance Summary', this.margin, currentY)
    currentY += 10

    // Score box
    const scoreBoxWidth = 60
    const scoreBoxHeight = 40
    const scoreBoxX = this.pageWidth - this.margin - scoreBoxWidth

    // Score background color based on performance
    const scoreColor = result.percentage >= 90 ? [34, 197, 94] : 
                      result.percentage >= 70 ? [59, 130, 246] :
                      result.percentage >= 50 ? [245, 158, 11] : [239, 68, 68]

    this.doc.setFillColor(scoreColor[0], scoreColor[1], scoreColor[2])
    this.doc.rect(scoreBoxX, currentY, scoreBoxWidth, scoreBoxHeight, 'F')

    // Score text
    this.doc.setTextColor(255, 255, 255)
    this.doc.setFontSize(24)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text(`${result.percentage}%`, scoreBoxX + 15, currentY + 20)
    this.doc.setFontSize(12)
    this.doc.text('Final Score', scoreBoxX + 10, currentY + 30)

    // Performance details
    this.doc.setTextColor(0, 0, 0)
    const performanceData = [
      ['Correct Answers', `${result.questions.filter(q => q.isCorrect).length} / ${result.quiz.totalQuestions}`],
      ['Points Earned', `${result.score} / ${result.quiz.totalPoints}`],
      ['Accuracy', `${result.percentage}%`],
      ['Status', result.percentage >= 70 ? 'PASSED' : 'FAILED']
    ]

    this.doc.autoTable({
      startY: currentY,
      head: [['Metric', 'Value']],
      body: performanceData,
      theme: 'grid',
      headStyles: { fillColor: [59, 130, 246] },
      margin: { left: this.margin, right: this.margin },
      tableWidth: this.pageWidth - scoreBoxWidth - this.margin * 3
    })

    // Add new page for detailed questions
    this.doc.addPage()
    currentY = this.addHeader('Detailed Question Analysis')

    // Questions breakdown
    const questionsData = result.questions.map((q, index) => [
      (index + 1).toString(),
      q.type,
      q.isCorrect ? 'Correct' : 'Incorrect',
      `${q.pointsEarned} / ${q.points}`,
      q.isCorrect ? '✓' : '✗'
    ])

    this.doc.autoTable({
      startY: currentY,
      head: [['#', 'Type', 'Result', 'Points', 'Status']],
      body: questionsData,
      theme: 'striped',
      headStyles: { fillColor: [59, 130, 246] },
      margin: { left: this.margin, right: this.margin },
      columnStyles: {
        4: { halign: 'center' }
      },
      didParseCell: (data: any) => {
        if (data.column.index === 4) {
          data.cell.styles.textColor = data.cell.text[0] === '✓' ? [34, 197, 94] : [239, 68, 68]
        }
      }
    })

    // Add footer to all pages
    const pageCount = this.doc.getNumberOfPages()
    for (let i = 1; i <= pageCount; i++) {
      this.doc.setPage(i)
      this.addFooter()
    }

    return new Blob([this.doc.output('blob')], { type: 'application/pdf' })
  }

  // Generate Analytics Report PDF
  async generateAnalyticsReportPDF(data: AnalyticsData, studentName: string): Promise<Blob> {
    // Header
    let currentY = this.addHeader(
      'Learning Analytics Report',
      `Performance Analysis for ${studentName}`
    )

    // Overview Section
    this.doc.setFontSize(16)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('Performance Overview', this.margin, currentY)
    currentY += 10

    const overviewData = [
      ['Total Attempts', data.overview.totalAttempts.toString()],
      ['Average Score', `${data.overview.averageScore.toFixed(1)}%`],
      ['Total Time Spent', `${Math.round(data.overview.totalTimeSpent / 60)} hours`],
      ['Improvement Rate', `${data.overview.improvementRate > 0 ? '+' : ''}${data.overview.improvementRate.toFixed(1)}%`]
    ]

    this.doc.autoTable({
      startY: currentY,
      head: [['Metric', 'Value']],
      body: overviewData,
      theme: 'grid',
      headStyles: { fillColor: [59, 130, 246] },
      margin: { left: this.margin, right: this.margin }
    })

    currentY = this.doc.lastAutoTable.finalY + 15

    // Performance by Category
    this.doc.setFontSize(16)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('Performance by Category', this.margin, currentY)
    currentY += 10

    const categoryData = data.performanceByCategory.map(cat => [
      cat.category,
      cat.attempts.toString(),
      `${cat.averageScore.toFixed(1)}%`,
      `${cat.improvement > 0 ? '+' : ''}${cat.improvement.toFixed(1)}%`
    ])

    this.doc.autoTable({
      startY: currentY,
      head: [['Category', 'Attempts', 'Avg Score', 'Improvement']],
      body: categoryData,
      theme: 'striped',
      headStyles: { fillColor: [59, 130, 246] },
      margin: { left: this.margin, right: this.margin }
    })

    currentY = this.doc.lastAutoTable.finalY + 15

    // Performance by Difficulty
    this.doc.setFontSize(16)
    this.doc.setFont('helvetica', 'bold')
    this.doc.text('Performance by Difficulty', this.margin, currentY)
    currentY += 10

    const difficultyData = data.performanceByDifficulty.map(diff => [
      diff.difficulty,
      diff.attempts.toString(),
      `${diff.averageScore.toFixed(1)}%`,
      `${diff.successRate}%`
    ])

    this.doc.autoTable({
      startY: currentY,
      head: [['Difficulty', 'Attempts', 'Avg Score', 'Success Rate']],
      body: difficultyData,
      theme: 'grid',
      headStyles: { fillColor: [59, 130, 246] },
      margin: { left: this.margin, right: this.margin }
    })

    // Add new page for weekly progress
    this.doc.addPage()
    currentY = this.addHeader('Weekly Progress Analysis')

    const weeklyData = data.weeklyProgress.map(week => [
      week.week,
      week.attempts.toString(),
      `${week.averageScore.toFixed(1)}%`,
      `${week.timeSpent} min`
    ])

    this.doc.autoTable({
      startY: currentY,
      head: [['Week', 'Attempts', 'Avg Score', 'Time Spent']],
      body: weeklyData,
      theme: 'striped',
      headStyles: { fillColor: [59, 130, 246] },
      margin: { left: this.margin, right: this.margin }
    })

    // Add footer to all pages
    const pageCount = this.doc.getNumberOfPages()
    for (let i = 1; i <= pageCount; i++) {
      this.doc.setPage(i)
      this.addFooter()
    }

    return new Blob([this.doc.output('blob')], { type: 'application/pdf' })
  }

  // Generate Certificate PDF
  async generateCertificatePDF(data: {
    studentName: string
    quizTitle: string
    score: number
    completedAt: string
    certificateId: string
  }): Promise<Blob> {
    // Use landscape orientation for certificate
    this.doc = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4'
    })

    this.pageWidth = this.doc.internal.pageSize.getWidth()
    this.pageHeight = this.doc.internal.pageSize.getHeight()

    // Certificate border
    this.doc.setLineWidth(3)
    this.doc.setDrawColor(59, 130, 246)
    this.doc.rect(10, 10, this.pageWidth - 20, this.pageHeight - 20)

    // Inner border
    this.doc.setLineWidth(1)
    this.doc.rect(15, 15, this.pageWidth - 30, this.pageHeight - 30)

    // Certificate title
    this.doc.setFontSize(36)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setTextColor(59, 130, 246)
    const titleText = 'CERTIFICATE OF COMPLETION'
    const titleWidth = this.doc.getTextWidth(titleText)
    this.doc.text(titleText, (this.pageWidth - titleWidth) / 2, 50)

    // Decorative line
    this.doc.setLineWidth(2)
    this.doc.setDrawColor(59, 130, 246)
    this.doc.line(50, 60, this.pageWidth - 50, 60)

    // Student name
    this.doc.setFontSize(28)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setTextColor(0, 0, 0)
    const nameText = data.studentName
    const nameWidth = this.doc.getTextWidth(nameText)
    this.doc.text(nameText, (this.pageWidth - nameWidth) / 2, 90)

    // Achievement text
    this.doc.setFontSize(16)
    this.doc.setFont('helvetica', 'normal')
    const achievementText = 'has successfully completed the quiz'
    const achievementWidth = this.doc.getTextWidth(achievementText)
    this.doc.text(achievementText, (this.pageWidth - achievementWidth) / 2, 110)

    // Quiz title
    this.doc.setFontSize(24)
    this.doc.setFont('helvetica', 'bold')
    this.doc.setTextColor(59, 130, 246)
    const quizTitleWidth = this.doc.getTextWidth(data.quizTitle)
    this.doc.text(data.quizTitle, (this.pageWidth - quizTitleWidth) / 2, 130)

    // Score
    this.doc.setFontSize(18)
    this.doc.setFont('helvetica', 'normal')
    this.doc.setTextColor(0, 0, 0)
    const scoreText = `with a score of ${data.score}%`
    const scoreWidth = this.doc.getTextWidth(scoreText)
    this.doc.text(scoreText, (this.pageWidth - scoreWidth) / 2, 150)

    // Date
    this.doc.setFontSize(14)
    const dateText = `Completed on ${new Date(data.completedAt).toLocaleDateString()}`
    const dateWidth = this.doc.getTextWidth(dateText)
    this.doc.text(dateText, (this.pageWidth - dateWidth) / 2, 170)

    // Certificate ID
    this.doc.setFontSize(10)
    this.doc.setTextColor(128, 128, 128)
    this.doc.text(`Certificate ID: ${data.certificateId}`, 20, this.pageHeight - 25)

    return new Blob([this.doc.output('blob')], { type: 'application/pdf' })
  }

  // Capture HTML element as PDF
  async captureElementAsPDF(elementId: string, _filename: string = 'document.pdf'): Promise<Blob> {
    const element = document.getElementById(elementId)
    if (!element) {
      throw new Error(`Element with ID '${elementId}' not found`)
    }

    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      allowTaint: true
    })

    const imgData = canvas.toDataURL('image/png')
    const imgWidth = 210 // A4 width in mm
    const pageHeight = 295 // A4 height in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width
    let heightLeft = imgHeight

    this.doc = new jsPDF('portrait', 'mm', 'a4')
    let position = 0

    this.doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
    heightLeft -= pageHeight

    while (heightLeft >= 0) {
      position = heightLeft - imgHeight
      this.doc.addPage()
      this.doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight
    }

    return new Blob([this.doc.output('blob')], { type: 'application/pdf' })
  }

  // Download PDF
  downloadPDF(blob: Blob, filename: string) {
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }
}

// Utility functions
export const generateQuizResultPDF = async (result: QuizResult): Promise<Blob> => {
  const generator = new PDFGenerator({
    title: `Quiz Result - ${result.quiz.title}`,
    author: result.student.name,
    subject: 'Quiz Result Report',
    creator: 'Quiz Platform'
  })
  return generator.generateQuizResultPDF(result)
}

export const generateAnalyticsReportPDF = async (data: AnalyticsData, studentName: string): Promise<Blob> => {
  const generator = new PDFGenerator({
    title: `Analytics Report - ${studentName}`,
    author: studentName,
    subject: 'Learning Analytics Report',
    creator: 'Quiz Platform'
  })
  return generator.generateAnalyticsReportPDF(data, studentName)
}

export const generateCertificatePDF = async (data: {
  studentName: string
  quizTitle: string
  score: number
  completedAt: string
  certificateId: string
}): Promise<Blob> => {
  const generator = new PDFGenerator({
    title: `Certificate - ${data.studentName}`,
    author: data.studentName,
    subject: 'Certificate of Completion',
    creator: 'Quiz Platform'
  })
  return generator.generateCertificatePDF(data)
}

export const captureElementAsPDF = async (elementId: string): Promise<Blob> => {
  const generator = new PDFGenerator()
  return generator.captureElementAsPDF(elementId)
}

export const downloadPDF = (blob: Blob, filename: string) => {
  const generator = new PDFGenerator()
  generator.downloadPDF(blob, filename)
}

export { PDFGenerator }
export type { QuizResult, AnalyticsData }
