"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { 
  Key, 
  Plus, 
  Trash2, 
  Eye, 
  EyeOff,
  Copy,
  CheckCircle,
  AlertTriangle,
  BarChart3,
  Calendar,
  Shield
} from "lucide-react"
import { toast } from "sonner"

interface ApiKey {
  id: string
  name: string
  keyPrefix: string
  permissions: string[]
  isActive: boolean
  lastUsedAt?: string
  expiresAt?: string
  createdAt: string
  usage?: {
    totalRequests: number
    last24Hours: number
    avgResponseTime: number
  }
}

export default function APIKeysPage() {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isCreating, setIsCreating] = useState(false)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [newApiKey, setNewApiKey] = useState<string | null>(null)
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set())

  const [createForm, setCreateForm] = useState({
    name: '',
    permissions: [] as string[],
    expiresAt: ''
  })

  const availablePermissions = [
    { key: 'quizzes:read', label: 'Read Quizzes', description: 'View quiz information' },
    { key: 'quizzes:write', label: 'Write Quizzes', description: 'Create and update quizzes' },
    { key: 'users:read', label: 'Read Users', description: 'View user information' },
    { key: 'notifications:send', label: 'Send Notifications', description: 'Send notifications to users' },
    { key: 'analytics:read', label: 'Read Analytics', description: 'Access analytics data' },
    { key: 'files:upload', label: 'Upload Files', description: 'Upload and manage files' }
  ]

  useEffect(() => {
    loadApiKeys()
  }, [])

  const loadApiKeys = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/admin/api-keys')
      if (response.ok) {
        const data = await response.json()
        setApiKeys(data.data)
      } else {
        toast.error('Failed to load API keys')
      }
    } catch (error) {
      console.error('Error loading API keys:', error)
      toast.error('Failed to load API keys')
    } finally {
      setIsLoading(false)
    }
  }

  const createApiKey = async () => {
    if (!createForm.name.trim()) {
      toast.error('Please enter a name for the API key')
      return
    }

    if (createForm.permissions.length === 0) {
      toast.error('Please select at least one permission')
      return
    }

    try {
      setIsCreating(true)
      const response = await fetch('/api/admin/api-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: createForm.name,
          permissions: createForm.permissions,
          expiresAt: createForm.expiresAt || undefined
        })
      })

      if (response.ok) {
        const data = await response.json()
        setNewApiKey(data.data.key)
        setApiKeys(prev => [data.data.apiKey, ...prev])
        setCreateForm({ name: '', permissions: [], expiresAt: '' })
        toast.success('API key created successfully!')
      } else {
        const error = await response.json()
        toast.error(error.error?.message || 'Failed to create API key')
      }
    } catch (error) {
      console.error('Error creating API key:', error)
      toast.error('Failed to create API key')
    } finally {
      setIsCreating(false)
    }
  }

  const revokeApiKey = async (keyId: string) => {
    if (!confirm('Are you sure you want to revoke this API key? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/admin/api-keys/${keyId}`, {
        method: 'PATCH'
      })

      if (response.ok) {
        setApiKeys(prev => prev.map(key => 
          key.id === keyId ? { ...key, isActive: false } : key
        ))
        toast.success('API key revoked successfully')
      } else {
        toast.error('Failed to revoke API key')
      }
    } catch (error) {
      console.error('Error revoking API key:', error)
      toast.error('Failed to revoke API key')
    }
  }

  const deleteApiKey = async (keyId: string) => {
    if (!confirm('Are you sure you want to delete this API key? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/admin/api-keys/${keyId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setApiKeys(prev => prev.filter(key => key.id !== keyId))
        toast.success('API key deleted successfully')
      } else {
        toast.error('Failed to delete API key')
      }
    } catch (error) {
      console.error('Error deleting API key:', error)
      toast.error('Failed to delete API key')
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast.success('Copied to clipboard!')
    } catch (error) {
      toast.error('Failed to copy to clipboard')
    }
  }

  const toggleKeyVisibility = (keyId: string) => {
    setVisibleKeys(prev => {
      const newSet = new Set(prev)
      if (newSet.has(keyId)) {
        newSet.delete(keyId)
      } else {
        newSet.add(keyId)
      }
      return newSet
    })
  }

  const togglePermission = (permission: string) => {
    setCreateForm(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission]
    }))
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Key className="h-8 w-8 text-blue-600" />
            API Key Management
          </h1>
          <p className="text-muted-foreground mt-1">
            Create and manage API keys for third-party integrations
          </p>
        </div>
        
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create API Key
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New API Key</DialogTitle>
              <DialogDescription>
                Generate a new API key for programmatic access to the platform
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-6">
              <div>
                <label className="text-sm font-medium mb-2 block">Name</label>
                <Input
                  value={createForm.name}
                  onChange={(e) => setCreateForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., Mobile App Integration"
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Permissions</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {availablePermissions.map(permission => (
                    <div
                      key={permission.key}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        createForm.permissions.includes(permission.key)
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => togglePermission(permission.key)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-sm">{permission.label}</div>
                          <div className="text-xs text-muted-foreground">{permission.description}</div>
                        </div>
                        {createForm.permissions.includes(permission.key) && (
                          <CheckCircle className="h-4 w-4 text-primary" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Expiration (Optional)</label>
                <Input
                  type="datetime-local"
                  value={createForm.expiresAt}
                  onChange={(e) => setCreateForm(prev => ({ ...prev, expiresAt: e.target.value }))}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Leave empty for no expiration
                </p>
              </div>

              <div className="flex justify-end gap-3">
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={createApiKey} disabled={isCreating}>
                  {isCreating ? 'Creating...' : 'Create API Key'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* New API Key Display */}
      {newApiKey && (
        <Card className="border-green-200 bg-green-50 dark:bg-green-900/20">
          <CardHeader>
            <CardTitle className="text-green-800 dark:text-green-200 flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              API Key Created Successfully
            </CardTitle>
            <CardDescription className="text-green-700 dark:text-green-300">
              Please copy your API key now. You won't be able to see it again.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2 p-3 bg-white dark:bg-gray-800 rounded-lg border">
              <code className="flex-1 font-mono text-sm">{newApiKey}</code>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(newApiKey)}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="mt-3"
              onClick={() => setNewApiKey(null)}
            >
              I've saved the key
            </Button>
          </CardContent>
        </Card>
      )}

      {/* API Keys List */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading API keys...</p>
          </div>
        ) : apiKeys.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <Key className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No API Keys</h3>
                <p className="text-muted-foreground mb-4">
                  Create your first API key to start integrating with the platform
                </p>
                <Button onClick={() => setShowCreateDialog(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create API Key
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          apiKeys.map((apiKey) => (
            <Card key={apiKey.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      {apiKey.name}
                      {!apiKey.isActive && (
                        <Badge variant="destructive">Revoked</Badge>
                      )}
                      {apiKey.expiresAt && new Date(apiKey.expiresAt) < new Date() && (
                        <Badge variant="destructive">Expired</Badge>
                      )}
                    </CardTitle>
                    <CardDescription>
                      Created {formatDate(apiKey.createdAt)}
                      {apiKey.lastUsedAt && (
                        <> • Last used {formatDate(apiKey.lastUsedAt)}</>
                      )}
                    </CardDescription>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleKeyVisibility(apiKey.id)}
                    >
                      {visibleKeys.has(apiKey.id) ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                    
                    {apiKey.isActive && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => revokeApiKey(apiKey.id)}
                      >
                        <AlertTriangle className="h-4 w-4" />
                      </Button>
                    )}
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteApiKey(apiKey.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-4">
                  {/* API Key Display */}
                  <div>
                    <label className="text-sm font-medium mb-2 block">API Key</label>
                    <div className="flex items-center gap-2 p-2 bg-muted rounded-lg">
                      <code className="flex-1 font-mono text-sm">
                        {visibleKeys.has(apiKey.id) 
                          ? `${apiKey.keyPrefix}${'*'.repeat(32)}`
                          : `${apiKey.keyPrefix}${'*'.repeat(32)}`
                        }
                      </code>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(`${apiKey.keyPrefix}${'*'.repeat(32)}`)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Permissions */}
                  <div>
                    <label className="text-sm font-medium mb-2 block">Permissions</label>
                    <div className="flex flex-wrap gap-2">
                      {apiKey.permissions.map(permission => (
                        <Badge key={permission} variant="outline" className="text-xs">
                          <Shield className="h-3 w-3 mr-1" />
                          {permission}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Usage Statistics */}
                  {apiKey.usage && (
                    <div>
                      <label className="text-sm font-medium mb-2 block">Usage Statistics</label>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div className="text-center p-2 bg-muted rounded">
                          <div className="font-medium">{apiKey.usage.totalRequests.toLocaleString()}</div>
                          <div className="text-muted-foreground">Total Requests</div>
                        </div>
                        <div className="text-center p-2 bg-muted rounded">
                          <div className="font-medium">{apiKey.usage.last24Hours}</div>
                          <div className="text-muted-foreground">Last 24h</div>
                        </div>
                        <div className="text-center p-2 bg-muted rounded">
                          <div className="font-medium">{apiKey.usage.avgResponseTime}ms</div>
                          <div className="text-muted-foreground">Avg Response</div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Expiration */}
                  {apiKey.expiresAt && (
                    <div>
                      <label className="text-sm font-medium mb-2 block">Expiration</label>
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="h-4 w-4" />
                        <span>{formatDate(apiKey.expiresAt)}</span>
                        {new Date(apiKey.expiresAt) < new Date() && (
                          <Badge variant="destructive" className="text-xs">Expired</Badge>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
