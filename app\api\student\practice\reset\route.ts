import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse, rateLimits } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// POST /api/student/practice/reset - Reset student's practice progress
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    rateLimit: rateLimits.strict
  },
  async (request: NextRequest, { user }) => {
    try {
      // Reset user practice statistics
      await prisma.userPracticeStats.upsert({
        where: { userId: user.id },
        update: {
          currentStreak: 0,
          longestStreak: 0,
          totalSessions: 0,
          lastPracticeDate: null,
          updatedAt: new Date()
        },
        create: {
          userId: user.id,
          currentStreak: 0,
          longestStreak: 0,
          totalSessions: 0,
          lastPracticeDate: null
        }
      })

      // Delete all practice quiz attempts (DAILY_PRACTICE type)
      await prisma.quizAttempt.deleteMany({
        where: {
          userId: user.id,
          quiz: {
            type: 'DAILY_PRACTICE'
          }
        }
      })

      // Reset user's total points and average score related to practice
      const remainingAttempts = await prisma.quizAttempt.findMany({
        where: {
          userId: user.id,
          completedAt: { not: null }
        },
        select: {
          score: true,
          totalQuestions: true
        }
      })

      let newTotalPoints = 0
      let newAverageScore = 0

      if (remainingAttempts.length > 0) {
        const totalScore = remainingAttempts.reduce((sum, attempt) => sum + (attempt.score || 0), 0)
        newAverageScore = Math.round(totalScore / remainingAttempts.length)
        newTotalPoints = remainingAttempts.reduce((sum, attempt) => {
          const percentage = attempt.totalQuestions ? (attempt.score || 0) / attempt.totalQuestions * 100 : 0
          return sum + Math.round(percentage * 10) // 10 points per percentage point
        }, 0)
      }

      // Update user stats
      await prisma.user.update({
        where: { id: user.id },
        data: {
          totalPoints: newTotalPoints,
          averageScore: newAverageScore,
          updatedAt: new Date()
        }
      })

      return APIResponse.success(
        {
          message: 'Practice progress reset successfully',
          newStats: {
            currentStreak: 0,
            longestStreak: 0,
            totalSessions: 0,
            totalPoints: newTotalPoints,
            averageScore: newAverageScore
          }
        },
        'Practice progress reset successfully'
      )

    } catch (error) {
      console.error('Error resetting practice progress:', error)
      return APIResponse.error('Failed to reset practice progress', 500)
    }
  }
)
