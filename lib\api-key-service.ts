import { prisma } from '@/lib/prisma'
import { randomBytes, createHash } from 'crypto'

export interface CreateApiKeyData {
  name: string
  permissions: string[]
  expiresAt?: Date
  userId: string
}

export interface ApiKeyInfo {
  id: string
  name: string
  keyPrefix: string
  permissions: string[]
  isActive: boolean
  lastUsedAt?: Date
  expiresAt?: Date
  createdAt: Date
  usage?: {
    totalRequests: number
    last24Hours: number
    avgResponseTime: number
  }
}

export class ApiKeyService {
  private static readonly KEY_LENGTH = 32
  private static readonly PREFIX_LENGTH = 12 // Increased for better uniqueness

  /**
   * Generate a new API key
   */
  static generateApiKey(): { key: string; keyPrefix: string; hashedKey: string } {
    const key = `qp_${randomBytes(this.KEY_LENGTH).toString('hex')}`
    const keyPrefix = key.substring(0, this.PREFIX_LENGTH)
    const hashedKey = this.hashKey(key)
    
    return { key, keyPrefix, hashedKey }
  }

  /**
   * Hash an API key for secure storage
   */
  static hashKey(key: string): string {
    return createHash('sha256').update(key).digest('hex')
  }

  /**
   * Create a new API key
   */
  static async createApiKey(data: CreateApiKeyData): Promise<{ apiKey: ApiKeyInfo; key: string }> {
    const { key, keyPrefix, hashedKey } = this.generateApiKey()

    const apiKey = await prisma.apiKey.create({
      data: {
        name: data.name,
        key: hashedKey,
        keyPrefix,
        permissions: data.permissions,
        expiresAt: data.expiresAt,
        userId: data.userId
      },
      include: {
        user: {
          select: { name: true, email: true }
        }
      }
    })

    return {
      apiKey: {
        id: apiKey.id,
        name: apiKey.name,
        keyPrefix: apiKey.keyPrefix,
        permissions: apiKey.permissions,
        isActive: apiKey.isActive,
        lastUsedAt: apiKey.lastUsedAt ?? undefined,
        expiresAt: apiKey.expiresAt ?? undefined,
        createdAt: apiKey.createdAt
      },
      key // Return the unhashed key only once
    }
  }

  /**
   * Validate an API key and return key info
   */
  static async validateApiKey(key: string): Promise<ApiKeyInfo | null> {
    try {
      // Basic validation
      if (!key || typeof key !== 'string' || !key.startsWith('qp_')) {
        return null
      }

      const hashedKey = this.hashKey(key)

      const apiKey = await prisma.apiKey.findUnique({
        where: { key: hashedKey },
        include: {
          user: {
            select: { id: true, name: true, email: true, role: true }
          }
        }
      })

      if (!apiKey || !apiKey.isActive) {
        return null
      }

      // Check if key is expired
      if (apiKey.expiresAt && apiKey.expiresAt < new Date()) {
        return null
      }

      // Update last used timestamp (don't await to avoid slowing down the request)
      prisma.apiKey.update({
        where: { id: apiKey.id },
        data: { lastUsedAt: new Date() }
      }).catch(error => {
        console.error('Failed to update API key last used timestamp:', error)
      })

      return {
        id: apiKey.id,
        name: apiKey.name,
        keyPrefix: apiKey.keyPrefix,
        permissions: apiKey.permissions,
        isActive: apiKey.isActive,
        lastUsedAt: new Date(),
        expiresAt: apiKey.expiresAt ?? undefined,
        createdAt: apiKey.createdAt
      }
    } catch (error) {
      console.error('Error validating API key:', error)
      return null
    }
  }

  /**
   * Get all API keys for a user
   */
  static async getUserApiKeys(userId: string): Promise<ApiKeyInfo[]> {
    const apiKeys = await prisma.apiKey.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    })

    // Get usage statistics for each key
    const keysWithUsage = await Promise.all(
      apiKeys.map(async (apiKey) => {
        const usage = await this.getApiKeyUsage(apiKey.id)
        return {
          id: apiKey.id,
          name: apiKey.name,
          keyPrefix: apiKey.keyPrefix,
          permissions: apiKey.permissions,
          isActive: apiKey.isActive,
          lastUsedAt: apiKey.lastUsedAt ?? undefined,
          expiresAt: apiKey.expiresAt ?? undefined,
          createdAt: apiKey.createdAt,
          usage
        }
      })
    )

    return keysWithUsage
  }

  /**
   * Get usage statistics for an API key
   */
  static async getApiKeyUsage(apiKeyId: string) {
    const now = new Date()
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)

    const [totalRequests, last24Hours, avgResponseTime] = await Promise.all([
      // Total requests
      prisma.apiUsage.count({
        where: { apiKeyId }
      }),

      // Requests in last 24 hours
      prisma.apiUsage.count({
        where: {
          apiKeyId,
          createdAt: { gte: yesterday }
        }
      }),

      // Average response time
      prisma.apiUsage.aggregate({
        where: { apiKeyId },
        _avg: { responseTime: true }
      })
    ])

    return {
      totalRequests,
      last24Hours,
      avgResponseTime: Math.round(avgResponseTime._avg.responseTime || 0)
    }
  }

  /**
   * Revoke (deactivate) an API key
   */
  static async revokeApiKey(apiKeyId: string, userId: string): Promise<void> {
    await prisma.apiKey.updateMany({
      where: {
        id: apiKeyId,
        userId // Ensure user can only revoke their own keys
      },
      data: {
        isActive: false
      }
    })
  }

  /**
   * Delete an API key permanently
   */
  static async deleteApiKey(apiKeyId: string, userId: string): Promise<void> {
    await prisma.apiKey.deleteMany({
      where: {
        id: apiKeyId,
        userId // Ensure user can only delete their own keys
      }
    })
  }

  /**
   * Log API usage
   */
  static async logApiUsage(data: {
    apiKeyId: string
    endpoint: string
    method: string
    statusCode: number
    responseTime: number
    ipAddress: string
    userAgent?: string
  }): Promise<void> {
    try {
      await prisma.apiUsage.create({
        data: {
          apiKeyId: data.apiKeyId,
          endpoint: data.endpoint,
          method: data.method,
          statusCode: data.statusCode,
          responseTime: data.responseTime,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent
        }
      })
    } catch (error) {
      // Log error but don't fail the API request
      console.error('Failed to log API usage:', error)
    }
  }

  /**
   * Check if API key has permission
   */
  static hasPermission(apiKey: ApiKeyInfo, permission: string): boolean {
    // Check for wildcard permission
    if (apiKey.permissions.includes('*')) {
      return true
    }

    // Check for exact permission
    if (apiKey.permissions.includes(permission)) {
      return true
    }

    // Check for wildcard resource permissions (e.g., "quizzes:*")
    const [resource] = permission.split(':')
    if (apiKey.permissions.includes(`${resource}:*`)) {
      return true
    }

    return false
  }

  /**
   * Get API key analytics
   */
  static async getApiKeyAnalytics(apiKeyId: string, userId: string) {
    // Verify ownership
    const apiKey = await prisma.apiKey.findFirst({
      where: { id: apiKeyId, userId }
    })

    if (!apiKey) {
      throw new Error('API key not found')
    }

    const now = new Date()
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

    // Get usage over the last 7 days
    // Note: We can't group by createdAt directly since it's a DateTime field
    // Instead, we'll fetch the data and group it manually
    const usageData = await prisma.apiUsage.findMany({
      where: {
        apiKeyId,
        createdAt: { gte: sevenDaysAgo }
      },
      select: {
        createdAt: true,
        responseTime: true
      }
    })

    // Group by date manually
    const dailyUsageMap = new Map<string, { count: number; totalResponseTime: number }>()
    usageData.forEach(usage => {
      const date = usage.createdAt.toISOString().split('T')[0]
      if (!dailyUsageMap.has(date)) {
        dailyUsageMap.set(date, { count: 0, totalResponseTime: 0 })
      }
      const dayData = dailyUsageMap.get(date)!
      dayData.count++
      dayData.totalResponseTime += usage.responseTime
    })

    const dailyUsage = Array.from(dailyUsageMap.entries()).map(([date, data]) => ({
      createdAt: new Date(date),
      _count: { id: data.count },
      _avg: { responseTime: data.count > 0 ? data.totalResponseTime / data.count : 0 }
    }))

    // Get endpoint usage
    const endpointUsage = await prisma.apiUsage.groupBy({
      by: ['endpoint', 'method'],
      where: { apiKeyId },
      _count: { id: true },
      _avg: { responseTime: true },
      orderBy: { _count: { id: 'desc' } },
      take: 10
    })

    // Get status code distribution
    const statusCodes = await prisma.apiUsage.groupBy({
      by: ['statusCode'],
      where: { apiKeyId },
      _count: { id: true }
    })

    return {
      dailyUsage: dailyUsage.map(day => ({
        date: day.createdAt,
        requests: day._count.id,
        avgResponseTime: Math.round(day._avg.responseTime || 0)
      })),
      endpointUsage: endpointUsage.map(endpoint => ({
        endpoint: endpoint.endpoint,
        method: endpoint.method,
        requests: endpoint._count.id,
        avgResponseTime: Math.round(endpoint._avg.responseTime || 0)
      })),
      statusCodes: statusCodes.map(status => ({
        code: status.statusCode,
        count: status._count.id
      }))
    }
  }

  /**
   * Clean up old API usage logs (run periodically)
   */
  static async cleanupOldUsageLogs(daysToKeep: number = 90): Promise<void> {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)

    await prisma.apiUsage.deleteMany({
      where: {
        createdAt: { lt: cutoffDate }
      }
    })
  }
}

// Available permissions
export const API_PERMISSIONS = {
  // Quiz permissions
  'quizzes:read': 'Read quiz information',
  'quizzes:write': 'Create and update quizzes',
  'quizzes:delete': 'Delete quizzes',
  'quizzes:*': 'All quiz permissions',

  // User permissions
  'users:read': 'Read user information',
  'users:write': 'Update user information',
  'users:*': 'All user permissions',

  // Notification permissions
  'notifications:read': 'Read notifications',
  'notifications:send': 'Send notifications',
  'notifications:*': 'All notification permissions',

  // Analytics permissions
  'analytics:read': 'Read analytics data',
  'analytics:*': 'All analytics permissions',

  // File permissions
  'files:read': 'Read file information',
  'files:upload': 'Upload files',
  'files:delete': 'Delete files',
  'files:*': 'All file permissions',

  // Admin permissions
  'admin:*': 'All administrative permissions',

  // Wildcard
  '*': 'All permissions (use with caution)'
} as const

export type ApiPermission = keyof typeof API_PERMISSIONS
