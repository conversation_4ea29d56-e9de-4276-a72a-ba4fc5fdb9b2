# API Integration Guide for Mobile & Desktop Applications

## Overview

The Quiz Platform API is well-structured for integration with mobile and desktop applications. It provides comprehensive authentication, standardized responses, and extensive functionality for building cross-platform applications.

## Base URL
```
Production: https://your-domain.com/api
Development: http://localhost:3000/api
```

## Authentication

### 1. API Key Authentication (Recommended for Mobile/Desktop)

The platform supports API key authentication which is ideal for mobile and desktop applications:

```http
Authorization: Bearer qp_your_api_key_here
```

#### API Key Features:
- **Secure**: SHA-256 hashed storage
- **Permissions-based**: Granular permission control
- **Usage tracking**: Monitor API usage and performance
- **Expiration support**: Optional expiration dates
- **Rate limiting**: Built-in protection against abuse

#### Creating API Keys:
```javascript
// Admin endpoint to create API keys
POST /api/admin/api-keys
{
  "name": "Mobile App Key",
  "permissions": ["quiz:read", "quiz:attempt", "analytics:read"],
  "expiresAt": "2025-12-31T23:59:59Z"
}
```

### 2. Session Authentication (Web Only)
- Uses NextAuth.js with OAuth providers (Google, GitHub)
- Session-based with JWT tokens
- Automatic session management

## Response Format

All API responses follow a consistent format:

### Success Response:
```json
{
  "success": true,
  "data": { /* response data */ },
  "message": "Optional success message",
  "timestamp": "2024-01-25T10:30:00.000Z"
}
```

### Error Response:
```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "code": "ERROR_CODE",
    "details": { /* additional error details */ },
    "timestamp": "2024-01-25T10:30:00.000Z"
  }
}
```

## Core API Endpoints

### Authentication Endpoints
- `POST /api/auth/update-role` - Update user role
- `GET /api/auth/session` - Get current session (web only)

### Student Endpoints
- `GET /api/student/categories/subjects` - Get available subjects
- `GET /api/student/categories/chapters` - Get chapters by subject
- `GET /api/student/categories/topics` - Get topics by chapter
- `GET /api/student/quizzes` - Get available quizzes
- `POST /api/student/quizzes/[id]/attempt` - Start quiz attempt
- `PUT /api/student/quiz-attempts/[id]` - Submit quiz answers
- `GET /api/student/quiz-results/[attemptId]` - Get quiz results
- `GET /api/student/analytics` - Get student analytics
- `GET /api/student/practice` - Get practice sessions

### Admin Endpoints
- `GET /api/admin/users` - Manage users
- `GET /api/admin/quizzes` - Manage quizzes
- `GET /api/admin/categories` - Manage categories
- `GET /api/admin/analytics` - System analytics
- `GET /api/admin/settings` - System settings

### PDF Export Endpoints
- `GET /api/student/quiz-results/[attemptId]/pdf` - Export quiz result as PDF
- `GET /api/student/analytics/pdf` - Export analytics as PDF
- `POST /api/pdf/generate` - Generate custom PDFs

### Notification Endpoints
- `GET /api/notifications` - Get user notifications
- `PUT /api/notifications/[id]/read` - Mark notification as read
- `POST /api/notifications` - Send notification (admin only)

## Rate Limiting

The API implements rate limiting with different tiers:

```javascript
const rateLimits = {
  strict: { windowMs: 60000, maxRequests: 10 },    // 10 requests per minute
  moderate: { windowMs: 60000, maxRequests: 50 },  // 50 requests per minute
  lenient: { windowMs: 60000, maxRequests: 100 }   // 100 requests per minute
}
```

## Mobile/Desktop Integration Recommendations

### 1. Authentication Flow
```javascript
// 1. Obtain API key from admin
// 2. Store securely in app (keychain/secure storage)
// 3. Include in all requests

const apiClient = {
  baseURL: 'https://your-domain.com/api',
  headers: {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json'
  }
}
```

### 2. Error Handling
```javascript
async function apiRequest(endpoint, options = {}) {
  try {
    const response = await fetch(`${apiClient.baseURL}${endpoint}`, {
      ...options,
      headers: { ...apiClient.headers, ...options.headers }
    })
    
    const data = await response.json()
    
    if (!response.ok || !data.success) {
      throw new Error(data.error?.message || 'API request failed')
    }
    
    return data.data
  } catch (error) {
    console.error('API Error:', error)
    throw error
  }
}
```

### 3. Offline Support
- Cache quiz data locally
- Queue quiz attempts when offline
- Sync when connection restored
- Store user progress locally

### 4. Real-time Features
The API supports WebSocket connections for real-time features:
- Live quiz sessions
- Real-time notifications
- Progress updates

## Security Considerations

### 1. API Key Security
- Store API keys securely (keychain, encrypted storage)
- Never expose keys in client-side code
- Implement key rotation
- Monitor for unauthorized usage

### 2. Data Validation
- Validate all input data
- Sanitize user inputs
- Implement client-side validation
- Handle server-side validation errors

### 3. HTTPS Only
- Always use HTTPS in production
- Implement certificate pinning
- Validate SSL certificates

## Platform-Specific Considerations

### Mobile Apps (React Native, Flutter, Native)
- Use secure storage for API keys
- Implement background sync
- Handle network connectivity changes
- Optimize for mobile data usage

### Desktop Apps (Electron, Tauri, Native)
- Secure credential storage
- Auto-update mechanisms
- Cross-platform compatibility
- Local data caching

## Example Implementation

### React Native Example:
```javascript
import AsyncStorage from '@react-native-async-storage/async-storage'

class QuizAPI {
  constructor() {
    this.baseURL = 'https://your-domain.com/api'
    this.apiKey = null
  }
  
  async initialize() {
    this.apiKey = await AsyncStorage.getItem('api_key')
  }
  
  async getQuizzes() {
    return this.request('/student/quizzes')
  }
  
  async submitQuizAttempt(attemptId, answers) {
    return this.request(`/student/quiz-attempts/${attemptId}`, {
      method: 'PUT',
      body: JSON.stringify({ answers })
    })
  }
  
  async request(endpoint, options = {}) {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        ...options.headers
      }
    })
    
    const data = await response.json()
    
    if (!data.success) {
      throw new Error(data.error.message)
    }
    
    return data.data
  }
}
```

## Conclusion

The Quiz Platform API is well-designed for mobile and desktop integration with:
- ✅ Comprehensive API key authentication
- ✅ Standardized response format
- ✅ Rate limiting and security features
- ✅ Extensive endpoint coverage
- ✅ Real-time capabilities
- ✅ PDF generation support
- ✅ Analytics and reporting

The API provides all necessary functionality to build full-featured mobile and desktop applications while maintaining security and performance standards.
