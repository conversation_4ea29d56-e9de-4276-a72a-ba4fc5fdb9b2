import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { createAPIHandler, APIResponse, rateLimits } from '@/lib/api-middleware'
import { AnalyticsService } from '@/lib/analytics-service'

const exportQuerySchema = z.object({
  format: z.enum(['csv', 'json', 'pdf']).default('json'),
  period: z.enum(['7d', '30d', '90d', '1y']).default('30d'),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional()
})

// GET /api/analytics/export - Export analytics data
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.strict, // Limit exports to prevent abuse
    validateQuery: exportQuerySchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    const { format, period, startDate, endDate } = validatedQuery

    // Calculate time range
    let timeRange
    if (startDate && endDate) {
      timeRange = {
        start: new Date(startDate),
        end: new Date(endDate)
      }
    } else {
      const end = new Date()
      const start = new Date()
      
      switch (period) {
        case '7d':
          start.setDate(end.getDate() - 7)
          break
        case '30d':
          start.setDate(end.getDate() - 30)
          break
        case '90d':
          start.setDate(end.getDate() - 90)
          break
        case '1y':
          start.setFullYear(end.getFullYear() - 1)
          break
      }
      
      timeRange = { start, end }
    }

    // Gather all analytics data
    const [overview, engagement, quizMetrics, systemMetrics] = await Promise.all([
      AnalyticsService.getPlatformOverview(timeRange),
      AnalyticsService.getUserEngagementMetrics(timeRange),
      AnalyticsService.getQuizPerformanceMetrics(timeRange),
      AnalyticsService.getSystemHealthMetrics(timeRange)
    ])

    const analyticsData = {
      exportInfo: {
        generatedAt: new Date().toISOString(),
        period,
        timeRange,
        format
      },
      overview,
      engagement,
      quizMetrics,
      systemMetrics
    }

    switch (format) {
      case 'json':
        return new NextResponse(JSON.stringify(analyticsData, null, 2), {
          headers: {
            'Content-Type': 'application/json',
            'Content-Disposition': `attachment; filename="analytics-${period}-${new Date().toISOString().split('T')[0]}.json"`
          }
        })

      case 'csv':
        const csv = generateCSV(analyticsData)
        return new NextResponse(csv, {
          headers: {
            'Content-Type': 'text/csv',
            'Content-Disposition': `attachment; filename="analytics-${period}-${new Date().toISOString().split('T')[0]}.csv"`
          }
        })

      case 'pdf':
        const pdf = await generatePDF(analyticsData)
        return new NextResponse(pdf, {
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': `attachment; filename="analytics-report-${period}-${new Date().toISOString().split('T')[0]}.pdf"`
          }
        })

      default:
        return APIResponse.error('Invalid export format', 400, 'INVALID_FORMAT')
    }
  }
)

function generateCSV(data: any): string {
  const lines: string[] = []
  
  // Header
  lines.push('Analytics Export Report')
  lines.push(`Generated: ${data.exportInfo.generatedAt}`)
  lines.push(`Period: ${data.exportInfo.period}`)
  lines.push('')

  // Overview metrics
  lines.push('OVERVIEW METRICS')
  lines.push('Metric,Value')
  lines.push(`Total Users,${data.overview.totalUsers}`)
  lines.push(`Active Users,${data.overview.activeUsers}`)
  lines.push(`Total Quizzes,${data.overview.totalQuizzes}`)
  lines.push(`Total Attempts,${data.overview.totalAttempts}`)
  lines.push(`Total Questions,${data.overview.totalQuestions}`)
  lines.push(`Average Score,${data.overview.averageScore.toFixed(2)}%`)
  lines.push(`Completion Rate,${data.overview.completionRate.toFixed(2)}%`)
  lines.push(`Total Notifications,${data.overview.totalNotifications}`)
  lines.push(`Total Files,${data.overview.totalFiles}`)
  lines.push(`Total API Keys,${data.overview.totalApiKeys}`)
  lines.push('')

  // Daily active users
  if (data.engagement.dailyActiveUsers.length > 0) {
    lines.push('DAILY ACTIVE USERS')
    lines.push('Date,Active Users')
    data.engagement.dailyActiveUsers.forEach((item: any) => {
      lines.push(`${item.date},${item.count}`)
    })
    lines.push('')
  }

  // Top performing quizzes
  if (data.quizMetrics.topPerformingQuizzes.length > 0) {
    lines.push('TOP PERFORMING QUIZZES')
    lines.push('Title,Attempts,Average Score,Completion Rate,Difficulty,Type')
    data.quizMetrics.topPerformingQuizzes.forEach((quiz: any) => {
      lines.push(`"${quiz.title}",${quiz.attempts},${quiz.averageScore.toFixed(2)},${quiz.completionRate.toFixed(2)},${quiz.difficulty},${quiz.type}`)
    })
    lines.push('')
  }

  // API usage
  lines.push('API USAGE')
  lines.push('Metric,Value')
  lines.push(`Total Requests,${data.systemMetrics.apiUsage.totalRequests}`)
  lines.push(`Average Response Time,${data.systemMetrics.apiUsage.averageResponseTime.toFixed(2)}ms`)
  lines.push(`Error Rate,${data.systemMetrics.apiUsage.errorRate.toFixed(2)}%`)
  lines.push('')

  // Top API endpoints
  if (data.systemMetrics.apiUsage.topEndpoints.length > 0) {
    lines.push('TOP API ENDPOINTS')
    lines.push('Endpoint,Requests,Average Time (ms)')
    data.systemMetrics.apiUsage.topEndpoints.forEach((endpoint: any) => {
      lines.push(`${endpoint.endpoint},${endpoint.requests},${endpoint.avgTime}`)
    })
    lines.push('')
  }

  // File storage
  lines.push('FILE STORAGE')
  lines.push('Metric,Value')
  lines.push(`Total Files,${data.systemMetrics.fileStorage.totalFiles}`)
  lines.push(`Total Size (bytes),${data.systemMetrics.fileStorage.totalSize}`)
  lines.push('')

  // Notifications
  lines.push('NOTIFICATIONS')
  lines.push('Metric,Value')
  lines.push(`Total Sent,${data.systemMetrics.notifications.totalSent}`)
  lines.push(`Delivery Rate,${data.systemMetrics.notifications.deliveryRate.toFixed(2)}%`)
  lines.push(`Read Rate,${data.systemMetrics.notifications.readRate.toFixed(2)}%`)
  lines.push(`Click Rate,${data.systemMetrics.notifications.clickRate.toFixed(2)}%`)

  return lines.join('\n')
}

async function generatePDF(data: any): Promise<Buffer> {
  // This is a simplified PDF generation
  // In production, you'd use a library like puppeteer, jsPDF, or PDFKit
  
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Analytics Report</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { text-align: center; margin-bottom: 30px; }
        .section { margin-bottom: 30px; }
        .metric { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #eee; }
        .chart-placeholder { height: 200px; background: #f5f5f5; display: flex; align-items: center; justify-content: center; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f5f5f5; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Analytics Report</h1>
        <p>Generated: ${data.exportInfo.generatedAt}</p>
        <p>Period: ${data.exportInfo.period}</p>
      </div>

      <div class="section">
        <h2>Platform Overview</h2>
        <div class="metric"><span>Total Users</span><span>${data.overview.totalUsers.toLocaleString()}</span></div>
        <div class="metric"><span>Active Users</span><span>${data.overview.activeUsers.toLocaleString()}</span></div>
        <div class="metric"><span>Total Quizzes</span><span>${data.overview.totalQuizzes.toLocaleString()}</span></div>
        <div class="metric"><span>Total Attempts</span><span>${data.overview.totalAttempts.toLocaleString()}</span></div>
        <div class="metric"><span>Average Score</span><span>${data.overview.averageScore.toFixed(2)}%</span></div>
        <div class="metric"><span>Completion Rate</span><span>${data.overview.completionRate.toFixed(2)}%</span></div>
      </div>

      <div class="section">
        <h2>User Engagement</h2>
        <div class="chart-placeholder">Daily Active Users Chart</div>
        <div class="metric"><span>Day 1 Retention</span><span>${data.engagement.userRetention.day1.toFixed(2)}%</span></div>
        <div class="metric"><span>Average Session Duration</span><span>${Math.round(data.engagement.sessionDuration.average / 60)} minutes</span></div>
      </div>

      <div class="section">
        <h2>Top Performing Quizzes</h2>
        <table>
          <thead>
            <tr>
              <th>Quiz Title</th>
              <th>Attempts</th>
              <th>Average Score</th>
              <th>Completion Rate</th>
              <th>Difficulty</th>
            </tr>
          </thead>
          <tbody>
            ${data.quizMetrics.topPerformingQuizzes.slice(0, 10).map((quiz: any) => `
              <tr>
                <td>${quiz.title}</td>
                <td>${quiz.attempts}</td>
                <td>${quiz.averageScore.toFixed(2)}%</td>
                <td>${quiz.completionRate.toFixed(2)}%</td>
                <td>${quiz.difficulty}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>

      <div class="section">
        <h2>System Health</h2>
        <div class="metric"><span>Total API Requests</span><span>${data.systemMetrics.apiUsage.totalRequests.toLocaleString()}</span></div>
        <div class="metric"><span>Average Response Time</span><span>${data.systemMetrics.apiUsage.averageResponseTime.toFixed(2)}ms</span></div>
        <div class="metric"><span>API Error Rate</span><span>${data.systemMetrics.apiUsage.errorRate.toFixed(2)}%</span></div>
        <div class="metric"><span>Total Files</span><span>${data.systemMetrics.fileStorage.totalFiles.toLocaleString()}</span></div>
        <div class="metric"><span>Storage Used</span><span>${formatBytes(data.systemMetrics.fileStorage.totalSize)}</span></div>
        <div class="metric"><span>Notifications Sent</span><span>${data.systemMetrics.notifications.totalSent.toLocaleString()}</span></div>
        <div class="metric"><span>Notification Delivery Rate</span><span>${data.systemMetrics.notifications.deliveryRate.toFixed(2)}%</span></div>
      </div>
    </body>
    </html>
  `

  // In production, use puppeteer or similar to convert HTML to PDF
  // For now, return the HTML as a buffer (this won't be a valid PDF)
  return Buffer.from(htmlContent, 'utf-8')
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
