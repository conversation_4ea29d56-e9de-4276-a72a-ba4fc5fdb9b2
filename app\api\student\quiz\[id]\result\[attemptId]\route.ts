import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, rateLimits } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { scoreQuizAttempt } from '@/lib/quiz-scoring'

// GET /api/student/quiz/[id]/result/[attemptId] - Get quiz attempt results
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    rateLimit: rateLimits.moderate
  },
  async (request: NextRequest, { user, params }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string
    const attemptId = resolvedParams?.attemptId as string

    if (!quizId || !attemptId) {
      return APIResponse.error('Quiz ID and Attempt ID are required', 400)
    }

    try {
      // Get the quiz attempt with detailed information
      const attempt = await prisma.quizAttempt.findUnique({
        where: {
          id: attemptId,
          userId: user.id, // Ensure user can only access their own attempts
          quizId: quizId
        },
        include: {
          quiz: {
            include: {
              questions: {
                select: {
                  id: true,
                  type: true,
                  text: true,
                  options: true,
                  correctAnswer: true,
                  explanation: true,
                  points: true,
                  order: true
                },
                orderBy: { order: 'asc' }
              },
              creator: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      })

      if (!attempt) {
        return APIResponse.error('Quiz attempt not found', 404)
      }

      if (!attempt.isCompleted) {
        return APIResponse.error('Quiz attempt is not completed yet', 400)
      }

      // Calculate time spent
      const timeSpent = attempt.completedAt && attempt.startedAt
        ? Math.round((attempt.completedAt.getTime() - attempt.startedAt.getTime()) / (1000 * 60))
        : 0

      // Process answers and calculate detailed results
      const quiz = attempt as any // Type assertion to access included quiz
      // Get user answers from the quiz attempt (stored as JSON)
      const userAnswers = (attempt.answers as Record<string, any>) || {}

      // Convert database types and use scoring service to recreate results
      const questionsForResults = quiz.quiz.questions.map((q: any) => ({
        id: q.id,
        text: q.text,
        type: q.type as string,
        options: q.options,
        correctAnswer: q.correctAnswer,
        explanation: q.explanation || undefined,
        points: q.points,
        difficulty: q.difficulty as string,
        tags: q.tags
      }))

      // Use the scoring service to recreate question results
      // This ensures consistency with the completion API
      const scoringResult = scoreQuizAttempt(questionsForResults, userAnswers)
      const questionResults = scoringResult.questionResults

      // Calculate category-wise performance
      const categoryPerformance = new Map()
      questionResults.forEach((result: any) => {
        const categories = result.tags || ['General']
        categories.forEach((category: string) => {
          if (!categoryPerformance.has(category)) {
            categoryPerformance.set(category, {
              category,
              correct: 0,
              total: 0,
              points: 0,
              maxPoints: 0
            })
          }
          
          const categoryStats = categoryPerformance.get(category)
          categoryStats.total++
          categoryStats.maxPoints += result.maxPoints
          
          if (result.isCorrect) {
            categoryStats.correct++
            categoryStats.points += result.points
          }
        })
      })

      const categoryStats = Array.from(categoryPerformance.values()).map(stats => ({
        ...stats,
        percentage: Math.round((stats.correct / stats.total) * 100)
      }))

      // Calculate difficulty-wise performance
      const difficultyPerformance = new Map()
      questionResults.forEach((result: any) => {
        const difficulty = result.difficulty
        if (!difficultyPerformance.has(difficulty)) {
          difficultyPerformance.set(difficulty, {
            difficulty,
            correct: 0,
            total: 0
          })
        }
        
        const difficultyStats = difficultyPerformance.get(difficulty)
        difficultyStats.total++
        
        if (result.isCorrect) {
          difficultyStats.correct++
        }
      })

      const difficultyStats = Array.from(difficultyPerformance.values()).map(stats => ({
        ...stats,
        percentage: Math.round((stats.correct / stats.total) * 100)
      }))

      // Get user's rank for this quiz (simplified calculation)
      const betterAttempts = await prisma.quizAttempt.count({
        where: {
          quizId: quizId,
          percentage: { gt: attempt.percentage || 0 },
          isCompleted: true
        }
      })

      const totalAttempts = await prisma.quizAttempt.count({
        where: {
          quizId: quizId,
          isCompleted: true
        }
      })

      const rank = betterAttempts + 1
      const percentile = totalAttempts > 0 ? Math.round(((totalAttempts - rank + 1) / totalAttempts) * 100) : 0

      // Get user's other attempts for comparison
      const userAttempts = await prisma.quizAttempt.findMany({
        where: {
          userId: user.id,
          quizId: quizId,
          isCompleted: true
        },
        select: {
          id: true,
          score: true,
          percentage: true,
          completedAt: true
        },
        orderBy: { completedAt: 'desc' },
        take: 5
      })

      // Calculate correct answers from question results
      const correctAnswers = questionResults.filter((q: any) => q.isCorrect).length
      const totalQuestions = questionResults.length

      // Determine if user passed
      const passed = (attempt.percentage || 0) >= (quiz.quiz.passingScore || 0)

      // Build response to match frontend QuizResult interface exactly
      const result = {
        id: attempt.id,
        quizId: quiz.quiz.id,
        quiz: {
          title: quiz.quiz.title,
          description: quiz.quiz.description,
          type: quiz.quiz.type,
          difficulty: quiz.quiz.difficulty,
          duration: quiz.quiz.timeLimit || 30,
          passingScore: quiz.quiz.passingScore,
          totalPoints: quiz.quiz.questions.reduce((sum: number, q: any) => sum + q.points, 0)
        },
        student: {
          name: quiz.quiz.creator.name,
          email: quiz.quiz.creator.email
        },
        score: attempt.score,
        percentage: attempt.percentage,
        totalQuestions,
        correctAnswers,
        incorrectAnswers: totalQuestions - correctAnswers,
        unansweredQuestions: 0, // Calculate if needed
        timeSpent,
        startedAt: attempt.startedAt.toISOString(),
        completedAt: attempt.completedAt?.toISOString() || '',
        passed,
        rank,
        totalAttempts,
        questions: questionResults.map((q: any) => ({
          id: q.questionId,
          type: q.questionType,
          text: q.questionText,
          options: q.options,
          correctAnswer: q.correctAnswer,
          explanation: q.explanation,
          points: q.maxPoints,
          difficulty: q.difficulty,
          tags: q.tags,
          studentAnswer: q.selectedAnswer,
          isCorrect: q.isCorrect,
          pointsEarned: q.points
        })),
        feedback: generateRecommendations(questionResults, categoryStats).length > 0
          ? generateRecommendations(questionResults, categoryStats)[0].description
          : "Great job completing the quiz!",
        recommendations: generateRecommendations(questionResults, categoryStats).map((r: any) => r.description)
      }

      return APIResponse.success(result, 'Quiz results retrieved successfully')

    } catch (error) {
      console.error('Error fetching quiz results:', error)
      return APIResponse.error('Failed to fetch quiz results', 500)
    }
  }
)

// Helper function to generate recommendations based on performance
function generateRecommendations(questionResults: any[], categoryStats: any[]) {
  const recommendations = []

  // Find weak categories
  const weakCategories = categoryStats.filter(cat => cat.percentage < 70)
  if (weakCategories.length > 0) {
    recommendations.push({
      type: 'improvement',
      title: 'Focus on Weak Areas',
      description: `Consider reviewing ${weakCategories.map(c => c.category).join(', ')} topics where you scored below 70%.`
    })
  }

  // Check for time management
  const avgTimePerQuestion = questionResults.reduce((sum, q) => sum + q.timeSpent, 0) / questionResults.length
  if (avgTimePerQuestion > 2) {
    recommendations.push({
      type: 'time_management',
      title: 'Improve Time Management',
      description: 'You spent more time than average on questions. Practice with timed quizzes to improve speed.'
    })
  }

  // Check for difficulty patterns
  const hardQuestions = questionResults.filter(q => q.difficulty === 'HARD')
  const hardCorrect = hardQuestions.filter(q => q.isCorrect).length
  if (hardQuestions.length > 0 && (hardCorrect / hardQuestions.length) < 0.5) {
    recommendations.push({
      type: 'difficulty',
      title: 'Challenge Yourself',
      description: 'Consider practicing more advanced topics to improve performance on difficult questions.'
    })
  }

  return recommendations
}
