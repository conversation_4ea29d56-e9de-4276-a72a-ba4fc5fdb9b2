import { NextRequest } from 'next/server'
import { z } from 'zod'
import { createAPIHandler, APIResponse, rateLimits } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const querySchema = z.object({
  period: z.enum(['7d', '30d', '90d', 'all']).optional().default('30d')
})

// GET /api/admin/notifications/stats - Get notification statistics
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.moderate,
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    const { period } = validatedQuery

    // Calculate date range
    let dateFilter: any = {}
    if (period !== 'all') {
      const days = period === '7d' ? 7 : period === '30d' ? 30 : 90
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)
      dateFilter = {
        createdAt: {
          gte: startDate
        }
      }
    }

    try {
      // Get total notifications sent
      const totalSent = await prisma.notification.count({
        where: {
          sentAt: { not: null },
          ...dateFilter
        }
      })

      // Get delivery statistics
      const [totalDelivered, totalRead, totalClicked] = await Promise.all([
        prisma.userNotification.count({
          where: {
            deliveredAt: { not: null },
            notification: dateFilter.createdAt ? {
              createdAt: dateFilter.createdAt
            } : undefined
          }
        }),
        prisma.userNotification.count({
          where: {
            isRead: true,
            notification: dateFilter.createdAt ? {
              createdAt: dateFilter.createdAt
            } : undefined
          }
        }),
        prisma.userNotification.count({
          where: {
            isClicked: true,
            notification: dateFilter.createdAt ? {
              createdAt: dateFilter.createdAt
            } : undefined
          }
        })
      ])

      // Calculate rates
      const deliveryRate = totalSent > 0 ? Math.round((totalDelivered / totalSent) * 100) : 0
      const readRate = totalDelivered > 0 ? Math.round((totalRead / totalDelivered) * 100) : 0
      const clickRate = totalRead > 0 ? Math.round((totalClicked / totalRead) * 100) : 0

      // Get recent notifications with stats
      const recentNotifications = await prisma.notification.findMany({
        where: {
          sentAt: { not: null },
          ...dateFilter
        },
        take: 10,
        orderBy: {
          sentAt: 'desc'
        },
        include: {
          userNotifications: {
            select: {
              id: true,
              isRead: true,
              isClicked: true,
              deliveredAt: true
            }
          }
        }
      })

      // Process recent notifications with stats
      const recentNotificationsWithStats = recentNotifications.map(notification => {
        const recipientCount = notification.userNotifications.length
        const deliveredCount = notification.userNotifications.filter(un => un.deliveredAt).length
        const readCount = notification.userNotifications.filter(un => un.isRead).length
        const clickCount = notification.userNotifications.filter(un => un.isClicked).length

        return {
          id: notification.id,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          priority: notification.priority,
          category: notification.category,
          sentAt: notification.sentAt?.toISOString(),
          createdAt: notification.createdAt.toISOString(),
          recipientCount,
          deliveredCount,
          readCount,
          clickCount,
          deliveryRate: recipientCount > 0 ? Math.round((deliveredCount / recipientCount) * 100) : 0,
          readRate: deliveredCount > 0 ? Math.round((readCount / deliveredCount) * 100) : 0,
          clickRate: readCount > 0 ? Math.round((clickCount / readCount) * 100) : 0
        }
      })

      // Get notification type breakdown
      const typeBreakdown = await prisma.notification.groupBy({
        by: ['type'],
        where: {
          sentAt: { not: null },
          ...dateFilter
        },
        _count: {
          id: true
        }
      })

      // Get daily stats for the period (for charts)
      const dailyStats = await prisma.$queryRaw`
        SELECT 
          DATE(sent_at) as date,
          COUNT(*) as sent,
          COUNT(CASE WHEN un.delivered_at IS NOT NULL THEN 1 END) as delivered,
          COUNT(CASE WHEN un.is_read = true THEN 1 END) as read,
          COUNT(CASE WHEN un.is_clicked = true THEN 1 END) as clicked
        FROM notifications n
        LEFT JOIN user_notifications un ON n.id = un.notification_id
        WHERE n.sent_at IS NOT NULL
        ${period !== 'all' ? `AND n.sent_at >= NOW() - INTERVAL '${period === '7d' ? 7 : period === '30d' ? 30 : 90} days'` : ''}
        GROUP BY DATE(sent_at)
        ORDER BY date DESC
        LIMIT 30
      ` as any[]

      const stats = {
        overview: {
          totalSent,
          totalDelivered,
          totalRead,
          totalClicked,
          deliveryRate,
          readRate,
          clickRate
        },
        recentNotifications: recentNotificationsWithStats,
        typeBreakdown: typeBreakdown.map(item => ({
          type: item.type,
          count: item._count.id
        })),
        dailyStats: dailyStats.map(day => ({
          date: day.date,
          sent: Number(day.sent),
          delivered: Number(day.delivered),
          read: Number(day.read),
          clicked: Number(day.clicked)
        })),
        period
      }

      return APIResponse.success(stats, 'Notification statistics retrieved successfully')
    } catch (error) {
      console.error('Error fetching notification stats:', error)
      return APIResponse.error('Failed to fetch notification statistics', 500)
    }
  }
)
