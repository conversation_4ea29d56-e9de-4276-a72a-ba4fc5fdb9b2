import { NextRequest } from 'next/server'
import { z } from 'zod'
import { createAPIHandler, APIResponse, rateLimits } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const updateNotificationSchema = z.object({
  title: z.string().min(1, "Title is required").optional(),
  message: z.string().min(1, "Message is required").optional(),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).optional(),
  category: z.string().optional(),
  actionUrl: z.string().url().optional(),
  imageUrl: z.string().url().optional(),
  expiresAt: z.string().datetime().optional(),
  scheduledAt: z.string().datetime().optional()
})

// GET /api/admin/notifications/[id] - Get specific notification
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.moderate
  },
  async (request: NextRequest, { params }: { params: Promise<{ id: string } > }) => {
    const { id } = await params
    const notificationId = id

    const notification = await prisma.notification.findUnique({
      where: { id: notificationId },
      include: {
        userNotifications: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        }
      }
    })

    if (!notification) {
      return APIResponse.error('Notification not found', 404, 'NOTIFICATION_NOT_FOUND')
    }

    // Calculate stats
    const recipientCount = notification.userNotifications.length
    const deliveredCount = notification.userNotifications.filter(un => un.deliveredAt).length
    const readCount = notification.userNotifications.filter(un => un.isRead).length
    const clickCount = notification.userNotifications.filter(un => un.isClicked).length

    const result = {
      id: notification.id,
      type: notification.type,
      title: notification.title,
      message: notification.message,
      priority: notification.priority,
      category: notification.category,
      actionUrl: notification.actionUrl,
      imageUrl: notification.imageUrl,
      expiresAt: notification.expiresAt?.toISOString(),
      scheduledAt: notification.scheduledAt?.toISOString(),
      sentAt: notification.sentAt?.toISOString(),
      createdAt: notification.createdAt.toISOString(),
      updatedAt: notification.updatedAt.toISOString(),
      recipientCount,
      deliveredCount,
      readCount,
      clickCount,
      deliveryRate: recipientCount > 0 ? Math.round((deliveredCount / recipientCount) * 100) : 0,
      readRate: deliveredCount > 0 ? Math.round((readCount / deliveredCount) * 100) : 0,
      clickRate: readCount > 0 ? Math.round((clickCount / readCount) * 100) : 0,
      status: notification.sentAt ? 'sent' : notification.scheduledAt ? 'scheduled' : 'draft',
      recipients: notification.userNotifications.map(un => ({
        id: un.id,
        user: un.user,
        isRead: un.isRead,
        readAt: un.readAt?.toISOString(),
        isClicked: un.isClicked,
        clickedAt: un.clickedAt?.toISOString(),
        isDismissed: un.isDismissed,
        dismissedAt: un.dismissedAt?.toISOString(),
        deliveredAt: un.deliveredAt?.toISOString(),
        createdAt: un.createdAt.toISOString()
      }))
    }

    return APIResponse.success(result, 'Notification retrieved successfully')
  }
)

// PUT /api/admin/notifications/[id] - Update notification
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.moderate,
    validateBody: updateNotificationSchema
  },
  async (request: NextRequest, { params, validatedBody }: { params: Promise<{ id: string } >, validatedBody: any }) => {
    const { id } = await params
    const notificationId = id

    // Check if notification exists
    const existingNotification = await prisma.notification.findUnique({
      where: { id: notificationId }
    })

    if (!existingNotification) {
      return APIResponse.error('Notification not found', 404, 'NOTIFICATION_NOT_FOUND')
    }

    // Don't allow editing sent notifications
    if (existingNotification.sentAt) {
      return APIResponse.error(
        'Cannot edit notifications that have already been sent',
        400,
        'NOTIFICATION_ALREADY_SENT'
      )
    }

    // Convert date strings to Date objects
    const updateData = {
      ...validatedBody,
      ...(validatedBody.expiresAt && { expiresAt: new Date(validatedBody.expiresAt) }),
      ...(validatedBody.scheduledAt && { scheduledAt: new Date(validatedBody.scheduledAt) })
    }

    const updatedNotification = await prisma.notification.update({
      where: { id: notificationId },
      data: updateData
    })

    return APIResponse.success(
      {
        id: updatedNotification.id,
        type: updatedNotification.type,
        title: updatedNotification.title,
        message: updatedNotification.message,
        priority: updatedNotification.priority,
        category: updatedNotification.category,
        actionUrl: updatedNotification.actionUrl,
        imageUrl: updatedNotification.imageUrl,
        expiresAt: updatedNotification.expiresAt?.toISOString(),
        scheduledAt: updatedNotification.scheduledAt?.toISOString(),
        sentAt: updatedNotification.sentAt?.toISOString(),
        createdAt: updatedNotification.createdAt.toISOString(),
        updatedAt: updatedNotification.updatedAt.toISOString()
      },
      'Notification updated successfully'
    )
  }
)

// DELETE /api/admin/notifications/[id] - Delete notification
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.strict
  },
  async (request: NextRequest, { params }: { params: Promise<{ id: string } > }) => {
    const { id } = await params
    const notificationId = id

    // Check if notification exists
    const existingNotification = await prisma.notification.findUnique({
      where: { id: notificationId }
    })

    if (!existingNotification) {
      return APIResponse.error('Notification not found', 404, 'NOTIFICATION_NOT_FOUND')
    }

    await prisma.notification.delete({
      where: { id: notificationId }
    })

    return APIResponse.success(
      { deletedNotificationId: notificationId },
      'Notification deleted successfully'
    )
  }
)

// POST /api/admin/notifications/[id]/send - Send a draft or scheduled notification immediately
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.strict
  },
  async (request: NextRequest, { params }: { params: Promise<{ id: string } > }) => {
    const { id } = await params
    const notificationId = id

    // Check if notification exists and is not already sent
    const notification = await prisma.notification.findUnique({
      where: { id: notificationId },
      include: {
        userNotifications: true
      }
    })

    if (!notification) {
      return APIResponse.error('Notification not found', 404, 'NOTIFICATION_NOT_FOUND')
    }

    if (notification.sentAt) {
      return APIResponse.error(
        'Notification has already been sent',
        400,
        'NOTIFICATION_ALREADY_SENT'
      )
    }

    // Update notification as sent
    const updatedNotification = await prisma.notification.update({
      where: { id: notificationId },
      data: {
        sentAt: new Date(),
        scheduledAt: null // Clear scheduled time since we're sending now
      }
    })

    // Update user notifications as delivered
    await prisma.userNotification.updateMany({
      where: {
        notificationId: notificationId,
        deliveredAt: null
      },
      data: {
        deliveredAt: new Date()
      }
    })

    return APIResponse.success(
      {
        id: updatedNotification.id,
        sentAt: updatedNotification.sentAt?.toISOString(),
        recipientCount: notification.userNotifications.length
      },
      'Notification sent successfully'
    )
  }
)
