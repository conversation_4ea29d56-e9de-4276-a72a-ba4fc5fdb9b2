import { NextRequest } from 'next/server'
import { createAP<PERSON>Handler, APIResponse, rateLimits } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { scoreQuizAttempt } from '@/lib/quiz-scoring'
import { NotificationEvents } from '@/lib/notification-events'

// Helper function to update user stats
async function updateUserStats(userId: string, stats: {
  totalQuizzes: number
  totalPoints: number
  averageScore: number
  timeSpent: number
}) {
  // Get current user stats
  const currentUser = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      totalQuizzes: true,
      totalPoints: true,
      averageScore: true,
      totalTimeSpent: true
    }
  })

  if (!currentUser) return

  // Calculate new stats
  const newTotalQuizzes = currentUser.totalQuizzes + stats.totalQuizzes
  const newTotalPoints = currentUser.totalPoints + stats.totalPoints
  const newTotalTimeSpent = currentUser.totalTimeSpent + stats.timeSpent

  // Calculate new average score
  const newAverageScore = newTotalQuizzes > 0
    ? ((currentUser.averageScore * currentUser.totalQuizzes) + stats.averageScore) / newTotalQuizzes
    : stats.averageScore

  // Update user stats
  await prisma.user.update({
    where: { id: userId },
    data: {
      totalQuizzes: newTotalQuizzes,
      totalPoints: newTotalPoints,
      averageScore: Math.round(newAverageScore * 100) / 100, // Round to 2 decimal places
      totalTimeSpent: newTotalTimeSpent
    }
  })
}

// POST /api/student/quizzes/[id]/attempt/[attemptId]/complete - Complete quiz attempt
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    rateLimit: rateLimits.moderate
  },
  async (_request: NextRequest, { user, params }) => {
    const resolvedParams = await params
    const quizId = resolvedParams?.id as string
    const attemptId = resolvedParams?.attemptId as string

    if (!quizId || !attemptId) {
      return APIResponse.error('Quiz ID and Attempt ID are required', 400)
    }

    try {
      console.log(`Completing quiz attempt ${attemptId} for user ${user.id}`)

      // Verify attempt belongs to user and is active
      const attempt = await prisma.quizAttempt.findUnique({
        where: {
          id: attemptId,
          userId: user.id,
          quizId,
          isCompleted: false
        },
        include: {
          quiz: {
            include: {
              questions: {
                select: {
                  id: true,
                  correctAnswer: true,
                  points: true
                }
              }
            }
          }
        }
      })

      if (!attempt) {
        return APIResponse.error('Invalid or already completed attempt', 400)
      }

      // Calculate final score
      const userAnswers = attempt.answers as Record<string, any> || {}
      const questions = attempt.quiz.questions

      // Calculate score for each question
      console.log('User answers:', userAnswers)
      console.log('Questions with correct answers:', questions.map(q => ({ id: q.id, correctAnswer: q.correctAnswer })))

      // Get question details for proper type checking
      const fullQuestions = await prisma.question.findMany({
        where: { quizId },
        select: {
          id: true,
          text: true,
          type: true,
          options: true,
          correctAnswer: true,
          explanation: true,
          points: true,
          difficulty: true,
          tags: true
        }
      })

      // Use centralized scoring service
      // Convert database types to scoring service format
      const questionsForScoring = fullQuestions.map(q => ({
        id: q.id,
        text: q.text,
        type: q.type as string,
        options: q.options,
        correctAnswer: q.correctAnswer,
        explanation: q.explanation || undefined,
        points: q.points,
        difficulty: q.difficulty as string,
        tags: q.tags
      }))

      const scoringResult = scoreQuizAttempt(questionsForScoring, userAnswers)

      console.log(`Quiz scoring complete: ${scoringResult.correctAnswers}/${scoringResult.totalQuestions} correct, ${scoringResult.totalPoints}/${scoringResult.maxPossiblePoints} points, ${scoringResult.percentage}%`)

      // Extract values for database update
      const correctAnswers = scoringResult.correctAnswers
      const incorrectAnswers = scoringResult.incorrectAnswers
      const unansweredQuestions = scoringResult.unansweredQuestions
      const totalPoints = scoringResult.totalPoints
      const totalQuestions = scoringResult.totalQuestions
      const percentage = scoringResult.percentage
      const maxPossiblePoints = scoringResult.maxPossiblePoints

      // Note: Individual question results are not stored separately
      // The answers are already stored in the QuizAttempt.answers JSON field
      // The result API will use the scoring service to recreate detailed results

      // Complete the attempt
      const completedAttempt = await prisma.quizAttempt.update({
        where: { id: attemptId },
        data: {
          completedAt: new Date(),
          isCompleted: true,
          score: totalPoints,
          totalPoints: maxPossiblePoints,
          percentage,
          correctAnswers,
          incorrectAnswers,
          unansweredQuestions
        },
        include: {
          quiz: {
            select: {
              title: true,
              type: true,
              difficulty: true
            }
          }
        }
      })

      console.log(`Quiz attempt completed successfully: ${attemptId}`)
      console.log(`Final results: Score ${totalPoints}/${maxPossiblePoints}, ${percentage}%, ${correctAnswers}/${totalQuestions} correct`)

      // Update user stats
      try {
        await updateUserStats(user.id, {
          totalQuizzes: 1,
          totalPoints: totalPoints,
          averageScore: percentage,
          timeSpent: completedAttempt.timeSpent || 0
        })
      } catch (statsError) {
        console.error('Failed to update user stats:', statsError)
        // Don't fail the completion if stats update fails
      }

      // Send quiz completion notification
      try {
        await NotificationEvents.onQuizCompleted(
          user.id,
          completedAttempt.quiz.title,
          quizId,
          percentage
        )
      } catch (notificationError) {
        console.error('Failed to send completion notification:', notificationError)
        // Don't fail the completion if notification fails
      }

      return APIResponse.success(
        {
          attemptId: completedAttempt.id,
          score: totalPoints,
          totalPoints: maxPossiblePoints,
          percentage,
          correctAnswers,
          incorrectAnswers,
          unansweredQuestions,
          totalQuestions,
          completedAt: completedAttempt.completedAt?.toISOString(),
          quiz: {
            id: quizId,
            title: completedAttempt.quiz.title,
            type: completedAttempt.quiz.type,
            difficulty: completedAttempt.quiz.difficulty
          }
        },
        'Quiz completed successfully'
      )

    } catch (error) {
      console.error('Error completing quiz attempt:', error)
      console.error('Error details:', error instanceof Error ? error.message : String(error))

      return APIResponse.error(
        error instanceof Error ? error.message : 'Failed to complete quiz',
        500
      )
    }
  }
)
