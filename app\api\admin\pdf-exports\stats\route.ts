import { NextRequest } from 'next/server'
import { z } from 'zod'
import { createAPIHandler, APIResponse, rateLimits } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const querySchema = z.object({
  period: z.enum(['7d', '30d', '90d', 'all']).optional().default('30d')
})

// GET /api/admin/pdf-exports/stats - Get PDF export statistics
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.moderate,
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    const { period } = validatedQuery

    // Calculate date range
    let dateFilter: any = {}
    if (period !== 'all') {
      const days = period === '7d' ? 7 : period === '30d' ? 30 : 90
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)
      dateFilter = {
        createdAt: {
          gte: startDate
        }
      }
    }

    try {
      // Get total counts by status
      const [totalExports, completedExports, failedExports, pendingExports] = await Promise.all([
        prisma.pdfExport.count({
          where: dateFilter
        }),
        prisma.pdfExport.count({
          where: {
            status: 'completed',
            ...dateFilter
          }
        }),
        prisma.pdfExport.count({
          where: {
            status: 'failed',
            ...dateFilter
          }
        }),
        prisma.pdfExport.count({
          where: {
            status: { in: ['pending', 'processing'] },
            ...dateFilter
          }
        })
      ])

      // Get exports by type
      const exportsByType = await prisma.pdfExport.groupBy({
        by: ['type'],
        where: dateFilter,
        _count: {
          id: true
        }
      })

      // Get total file size
      const totalSizeResult = await prisma.pdfExport.aggregate({
        where: {
          status: 'completed',
          ...dateFilter
        },
        _sum: {
          size: true
        }
      })

      // Get recent exports
      const recentExports = await prisma.pdfExport.findMany({
        where: dateFilter,
        take: 10,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })

      // Get daily export counts for the period (for charts)
      const dailyExports = await prisma.$queryRaw`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed
        FROM pdf_exports
        WHERE 1=1
        ${period !== 'all' ? `AND created_at >= NOW() - INTERVAL '${period === '7d' ? 7 : period === '30d' ? 30 : 90} days'` : ''}
        GROUP BY DATE(created_at)
        ORDER BY date DESC
        LIMIT 30
      ` as any[]

      // Calculate success rate
      const successRate = totalExports > 0 ? Math.round((completedExports / totalExports) * 100) : 0

      // Format file size
      const totalSize = totalSizeResult._sum.size || 0
      const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 B'
        const k = 1024
        const sizes = ['B', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
      }

      const stats = {
        overview: {
          totalExports,
          completedExports,
          failedExports,
          pendingExports,
          successRate,
          totalSize: formatFileSize(totalSize),
          totalSizeBytes: totalSize
        },
        exportsByType: exportsByType.map(item => ({
          type: item.type,
          count: item._count.id
        })),
        recentExports: recentExports.map(pdfExport => ({
          id: pdfExport.id,
          type: pdfExport.type,
          filename: pdfExport.filename,
          size: pdfExport.size,
          status: pdfExport.status,
          createdAt: pdfExport.createdAt.toISOString(),
          user: pdfExport.user
        })),
        dailyExports: dailyExports.map(day => ({
          date: day.date,
          total: Number(day.total),
          completed: Number(day.completed),
          failed: Number(day.failed)
        })),
        period
      }

      return APIResponse.success(stats, 'PDF export statistics retrieved successfully')
    } catch (error) {
      console.error('Error fetching PDF export stats:', error)
      return APIResponse.error('Failed to fetch PDF export statistics', 500)
    }
  }
)
