import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, rateLimits } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateProfileSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  bio: z.string().max(500, 'Bio too long').optional(),
  location: z.string().max(100, 'Location too long').optional(),
  website: z.string().url('Invalid website URL').optional().or(z.literal('')),
  github: z.string().max(100, 'GitHub username too long').optional(),
  linkedin: z.string().max(100, 'LinkedIn username too long').optional(),
  twitter: z.string().max(100, 'Twitter username too long').optional(),
  preferences: z.object({
    emailNotifications: z.boolean().optional(),
    pushNotifications: z.boolean().optional(),
    weeklyDigest: z.boolean().optional(),
    achievementAlerts: z.boolean().optional(),
    theme: z.enum(['light', 'dark', 'system']).optional(),
    language: z.string().optional()
  }).optional()
})

// GET /api/student/profile - Get student profile
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    rateLimit: rateLimits.moderate
  },
  async (request: NextRequest, { user }) => {
    try {
      // Get user profile with additional stats
      const profile = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          bio: true,
          location: true,
          website: true,
          github: true,
          linkedin: true,
          twitter: true,
          points: true,
          totalPoints: true,
          totalQuizzes: true,
          averageScore: true,
          streak: true,
          preferences: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              quizAttempts: {
                where: { completedAt: { not: null } }
              },
              enrollments: true
            }
          }
        }
      })

      if (!profile) {
        return APIResponse.error('Profile not found', 404)
      }

      // Get recent activity
      const recentAttempts = await prisma.quizAttempt.findMany({
        where: {
          userId: user.id,
          completedAt: { not: null }
        },
        include: {
          quiz: {
            select: {
              title: true,
              type: true,
              difficulty: true
            }
          }
        },
        orderBy: { completedAt: 'desc' },
        take: 5
      })

      // Get achievements count
      const achievementsCount = await getAchievementsCount(user.id)

      // Calculate additional stats
      const thisMonth = new Date()
      thisMonth.setDate(1)
      thisMonth.setHours(0, 0, 0, 0)

      const monthlyAttempts = await prisma.quizAttempt.count({
        where: {
          userId: user.id,
          completedAt: { gte: thisMonth, not: null }
        }
      })

      const thisWeek = new Date()
      thisWeek.setDate(thisWeek.getDate() - thisWeek.getDay())
      thisWeek.setHours(0, 0, 0, 0)

      const weeklyAttempts = await prisma.quizAttempt.count({
        where: {
          userId: user.id,
          completedAt: { gte: thisWeek, not: null }
        }
      })

      // Get rank
      const rank = await prisma.user.count({
        where: {
          role: 'STUDENT',
          totalPoints: { gt: profile.totalPoints || 0 }
        }
      }) + 1

      // Calculate and update derived stats if needed
      await updateUserStats(user.id)

      // Transform data for frontend
      const transformedProfile = {
        id: profile.id,
        name: profile.name,
        email: profile.email,
        avatar: profile.image,
        bio: profile.bio,
        location: profile.location,
        website: profile.website,
        social: {
          github: profile.github,
          linkedin: profile.linkedin,
          twitter: profile.twitter
        },
        stats: {
          totalPoints: profile.totalPoints || 0,
          totalQuizzes: profile.totalQuizzes || 0,
          completedQuizzes: profile._count.quizAttempts,
          enrolledQuizzes: profile._count.enrollments,
          averageScore: profile.averageScore || 0,
          currentStreak: profile.streak || 0,
          rank,
          achievementsUnlocked: achievementsCount,
          monthlyQuizzes: monthlyAttempts,
          weeklyQuizzes: weeklyAttempts
        },
        preferences: profile.preferences || {
          emailNotifications: true,
          pushNotifications: true,
          weeklyDigest: true,
          achievementAlerts: true,
          theme: 'system',
          language: 'en'
        },
        recentActivity: recentAttempts.map(attempt => ({
          id: attempt.id,
          quiz: {
            title: attempt.quiz.title,
            type: attempt.quiz.type,
            difficulty: attempt.quiz.difficulty
          },
          score: attempt.percentage,
          completedAt: attempt.completedAt?.toISOString()
        })),
        joinedAt: profile.createdAt.toISOString(),
        lastUpdated: profile.updatedAt.toISOString()
      }

      return APIResponse.success(transformedProfile, 'Profile retrieved successfully')

    } catch (error) {
      console.error('Error fetching profile:', error)
      return APIResponse.error('Failed to fetch profile', 500)
    }
  }
)

// PUT /api/student/profile - Update student profile
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    rateLimit: rateLimits.strict,
    validateBody: updateProfileSchema
  },
  async (request: NextRequest, { user, validatedBody }) => {
    try {
      const updateData: any = {}

      // Handle basic profile fields
      if (validatedBody.name !== undefined) updateData.name = validatedBody.name
      if (validatedBody.bio !== undefined) updateData.bio = validatedBody.bio
      if (validatedBody.location !== undefined) updateData.location = validatedBody.location
      if (validatedBody.website !== undefined) updateData.website = validatedBody.website || null
      if (validatedBody.github !== undefined) updateData.github = validatedBody.github
      if (validatedBody.linkedin !== undefined) updateData.linkedin = validatedBody.linkedin
      if (validatedBody.twitter !== undefined) updateData.twitter = validatedBody.twitter

      // Handle preferences
      if (validatedBody.preferences) {
        updateData.preferences = validatedBody.preferences
      }

      // Update user profile
      const updatedProfile = await prisma.user.update({
        where: { id: user.id },
        data: updateData,
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          bio: true,
          location: true,
          website: true,
          github: true,
          linkedin: true,
          twitter: true,
          preferences: true,
          updatedAt: true
        }
      })

      return APIResponse.success(updatedProfile, 'Profile updated successfully')

    } catch (error) {
      console.error('Error updating profile:', error)
      return APIResponse.error('Failed to update profile', 500)
    }
  }
)

// Helper function to get achievements count
async function getAchievementsCount(userId: string): Promise<number> {
  // This would integrate with the achievements system
  // For now, return a simple count based on user stats
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      totalQuizzes: true,
      averageScore: true,
      streak: true,
      _count: {
        select: {
          quizAttempts: {
            where: { percentage: 100 }
          }
        }
      }
    }
  })

  if (!user) return 0

  let count = 0
  
  // Basic achievements
  if ((user.totalQuizzes || 0) >= 1) count++ // First quiz
  if ((user.totalQuizzes || 0) >= 10) count++ // 10 quizzes
  if ((user.totalQuizzes || 0) >= 50) count++ // 50 quizzes
  if ((user.averageScore || 0) >= 80) count++ // High achiever
  if ((user.averageScore || 0) >= 90) count++ // Expert
  if ((user.streak || 0) >= 7) count++ // Week warrior
  if (user._count.quizAttempts > 0) count++ // Perfect score

  return count
}

// Helper function to calculate and update user stats
async function updateUserStats(userId: string): Promise<void> {
  try {
    // Get all completed quiz attempts for the user
    const attempts = await prisma.quizAttempt.findMany({
      where: {
        userId,
        completedAt: { not: null }
      },
      orderBy: { completedAt: 'desc' }
    })

    // Calculate stats
    const totalQuizzes = attempts.length
    const totalPoints = attempts.reduce((sum, attempt) => sum + attempt.score, 0)
    const averageScore = totalQuizzes > 0 ? attempts.reduce((sum, attempt) => sum + attempt.percentage, 0) / totalQuizzes : 0

    // Calculate current streak
    let currentStreak = 0
    const today = new Date()
    today.setHours(23, 59, 59, 999) // End of today

    for (let i = 0; i < attempts.length; i++) {
      const attemptDate = new Date(attempts[i].completedAt!)
      const daysDiff = Math.floor((today.getTime() - attemptDate.getTime()) / (1000 * 60 * 60 * 24))

      if (daysDiff === currentStreak) {
        currentStreak++
      } else {
        break
      }
    }

    // Update user with calculated stats
    await prisma.user.update({
      where: { id: userId },
      data: {
        totalQuizzes,
        totalPoints,
        averageScore,
        streak: currentStreak,
        points: totalPoints // Keep points in sync with totalPoints
      }
    })
  } catch (error) {
    console.error('Error updating user stats:', error)
    // Don't throw error to avoid breaking the main request
  }
}
