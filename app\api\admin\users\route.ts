import { NextRequest } from 'next/server'
import { z } from 'zod'
import { createAPIHandler, APIResponse, rateLimits } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const createUserSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Valid email is required"),
  role: z.enum(['STUDENT', 'ADMIN']).default('STUDENT'),
  bio: z.string().optional()
})

const updateUserSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  email: z.string().email("Valid email is required").optional(),
  role: z.enum(['STUDENT', 'ADMIN']).optional(),
  bio: z.string().optional()
})

const userQuerySchema = z.object({
  page: z.string().transform(val => parseInt(val) || 1),
  limit: z.string().transform(val => Math.min(parseInt(val) || 10, 50)),
  search: z.string().optional(),
  role: z.enum(['STUDENT', 'ADMIN', 'all']).optional().default('all'),
  sortBy: z.enum(['name', 'email', 'createdAt', 'lastActive']).optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc')
})

// GET /api/admin/users - Get users with pagination and filtering
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.moderate,
    validateQuery: userQuerySchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    const { page, limit, search, role, sortBy, sortOrder } = validatedQuery
    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (role !== 'all') {
      where.role = role
    }

    // Build orderBy clause
    let orderBy: any = {}
    if (sortBy === 'lastActive') {
      // For last active, we'll use the most recent quiz attempt or session
      orderBy = { updatedAt: sortOrder }
    } else {
      orderBy[sortBy] = sortOrder
    }

    const [users, totalCount] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          quizAttempts: {
            select: {
              id: true,
              startedAt: true,
              score: true,
              percentage: true,
              isCompleted: true
            },
            orderBy: {
              startedAt: 'desc'
            },
            take: 1
          },
          _count: {
            select: {
              quizAttempts: true,
              quizzes: true
            }
          }
        }
      }),
      prisma.user.count({ where })
    ])

    // Calculate additional stats for each user
    const usersWithStats = await Promise.all(
      users.map(async (user) => {
        // Get average score from completed attempts
        const avgScoreResult = await prisma.quizAttempt.aggregate({
          where: {
            userId: user.id,
            isCompleted: true
          },
          _avg: {
            percentage: true
          }
        })

        // Get last activity (most recent quiz attempt or account update)
        const lastAttempt = user.quizAttempts[0]
        const lastActive = lastAttempt 
          ? lastAttempt.startedAt 
          : user.updatedAt

        return {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          bio: user.bio,
          image: user.image,
          points: user.points,
          level: user.level,
          createdAt: user.createdAt.toISOString(),
          updatedAt: user.updatedAt.toISOString(),
          lastActive: lastActive.toISOString(),
          stats: {
            quizzesCompleted: user._count.quizAttempts,
            quizzesCreated: user._count.quizzes,
            averageScore: Math.round(avgScoreResult._avg.percentage || 0)
          }
        }
      })
    )

    const totalPages = Math.ceil(totalCount / limit)

    return APIResponse.success({
      users: usersWithStats,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }, 'Users retrieved successfully')
  }
)

// POST /api/admin/users - Create new user
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.strict,
    validateBody: createUserSchema
  },
  async (request: NextRequest, { validatedBody }) => {
    const { name, email, role, bio } = validatedBody

    // Check if user with email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      return APIResponse.error(
        'User with this email already exists',
        400,
        'USER_EXISTS'
      )
    }

    const user = await prisma.user.create({
      data: {
        name,
        email,
        role,
        bio: bio || null
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        bio: true,
        image: true,
        points: true,
        level: true,
        createdAt: true,
        updatedAt: true
      }
    })

    return APIResponse.success(
      {
        ...user,
        createdAt: user.createdAt.toISOString(),
        updatedAt: user.updatedAt.toISOString(),
        lastActive: user.createdAt.toISOString(),
        stats: {
          quizzesCompleted: 0,
          quizzesCreated: 0,
          averageScore: 0
        }
      },
      'User created successfully',
      201
    )
  }
)

// DELETE /api/admin/users - Bulk delete users
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.strict,
    validateBody: z.object({
      userIds: z.array(z.string()).min(1, "At least one user ID is required")
    })
  },
  async (request: NextRequest, { validatedBody, user }) => {
    const { userIds } = validatedBody

    // Prevent admin from deleting themselves
    if (userIds.includes(user.id)) {
      return APIResponse.error(
        'Cannot delete your own account',
        400,
        'CANNOT_DELETE_SELF'
      )
    }

    // Check if any of the users are the only admin
    const adminCount = await prisma.user.count({
      where: { role: 'ADMIN' }
    })

    const adminsToDelete = await prisma.user.count({
      where: {
        id: { in: userIds },
        role: 'ADMIN'
      }
    })

    if (adminCount - adminsToDelete < 1) {
      return APIResponse.error(
        'Cannot delete all admin users. At least one admin must remain.',
        400,
        'CANNOT_DELETE_ALL_ADMINS'
      )
    }

    const deletedUsers = await prisma.user.deleteMany({
      where: {
        id: { in: userIds }
      }
    })

    return APIResponse.success(
      { deletedCount: deletedUsers.count },
      `Successfully deleted ${deletedUsers.count} user(s)`
    )
  }
)
