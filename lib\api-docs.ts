import { z } from 'zod'

export interface APIEndpoint {
  path: string
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'
  summary: string
  description: string
  tags: string[]
  requiresAuth: boolean
  requiredRole?: 'ADMIN' | 'STUDENT'
  parameters?: APIParameter[]
  requestBody?: APIRequestBody
  responses: APIResponse[]
  examples?: APIExample[]
  rateLimit?: {
    requests: number
    window: string
  }
}

export interface APIParameter {
  name: string
  in: 'path' | 'query' | 'header'
  required: boolean
  type: string
  description: string
  example?: any
  enum?: string[]
}

export interface APIRequestBody {
  required: boolean
  contentType: string
  schema: any
  example?: any
}

export interface APIResponse {
  status: number
  description: string
  schema?: any
  example?: any
}

export interface APIExample {
  name: string
  description: string
  request?: any
  response?: any
}

// API Documentation Registry
export class APIDocumentation {
  private static endpoints: APIEndpoint[] = []

  static register(endpoint: APIEndpoint) {
    this.endpoints.push(endpoint)
  }

  static getAll(): APIEndpoint[] {
    return this.endpoints
  }

  static getByTag(tag: string): APIEndpoint[] {
    return this.endpoints.filter(endpoint => endpoint.tags.includes(tag))
  }

  static generateOpenAPI(): any {
    return {
      openapi: '3.0.0',
      info: {
        title: 'Quiz Platform API',
        version: '1.0.0',
        description: 'Comprehensive API for the Quiz Platform with authentication, quizzes, notifications, and more.',
        contact: {
          name: 'API Support',
          email: '<EMAIL>'
        },
        license: {
          name: 'MIT',
          url: 'https://opensource.org/licenses/MIT'
        }
      },
      servers: [
        {
          url: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
          description: 'Development server'
        }
      ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT'
          },
          sessionAuth: {
            type: 'apiKey',
            in: 'cookie',
            name: 'next-auth.session-token'
          }
        },
        schemas: this.generateSchemas()
      },
      paths: this.generatePaths(),
      tags: this.generateTags()
    }
  }

  private static generatePaths(): any {
    const paths: any = {}

    this.endpoints.forEach(endpoint => {
      if (!paths[endpoint.path]) {
        paths[endpoint.path] = {}
      }

      paths[endpoint.path][endpoint.method.toLowerCase()] = {
        summary: endpoint.summary,
        description: endpoint.description,
        tags: endpoint.tags,
        security: endpoint.requiresAuth ? [{ sessionAuth: [] }] : [],
        parameters: endpoint.parameters?.map(param => ({
          name: param.name,
          in: param.in,
          required: param.required,
          schema: { type: param.type, enum: param.enum },
          description: param.description,
          example: param.example
        })),
        requestBody: endpoint.requestBody ? {
          required: endpoint.requestBody.required,
          content: {
            [endpoint.requestBody.contentType]: {
              schema: endpoint.requestBody.schema,
              example: endpoint.requestBody.example
            }
          }
        } : undefined,
        responses: endpoint.responses.reduce((acc, response) => {
          acc[response.status] = {
            description: response.description,
            content: response.schema ? {
              'application/json': {
                schema: response.schema,
                example: response.example
              }
            } : undefined
          }
          return acc
        }, {} as any)
      }
    })

    return paths
  }

  private static generateSchemas(): any {
    return {
      User: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          email: { type: 'string', format: 'email' },
          role: { type: 'string', enum: ['ADMIN', 'STUDENT'] },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      },
      Quiz: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          title: { type: 'string' },
          description: { type: 'string' },
          type: { type: 'string', enum: ['QUIZ', 'TEST_SERIES', 'DAILY_PRACTICE'] },
          difficulty: { type: 'string', enum: ['EASY', 'MEDIUM', 'HARD'] },
          tags: { type: 'array', items: { type: 'string' } },
          timeLimit: { type: 'integer' },
          maxAttempts: { type: 'integer' },
          passingScore: { type: 'number' },
          isPublished: { type: 'boolean' },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      },
      Question: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          text: { type: 'string' },
          type: { type: 'string', enum: ['MULTIPLE_CHOICE', 'TRUE_FALSE', 'SHORT_ANSWER'] },
          options: { type: 'array', items: { type: 'string' } },
          correctAnswer: { type: 'string' },
          explanation: { type: 'string' },
          points: { type: 'number' },
          order: { type: 'integer' }
        }
      },
      QuizAttempt: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          userId: { type: 'string' },
          quizId: { type: 'string' },
          answers: { type: 'object' },
          score: { type: 'number' },
          percentage: { type: 'number' },
          timeSpent: { type: 'integer' },
          isCompleted: { type: 'boolean' },
          startedAt: { type: 'string', format: 'date-time' },
          completedAt: { type: 'string', format: 'date-time' }
        }
      },
      Notification: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          type: { type: 'string' },
          title: { type: 'string' },
          message: { type: 'string' },
          actionUrl: { type: 'string' },
          imageUrl: { type: 'string' },
          priority: { type: 'string', enum: ['low', 'normal', 'high', 'urgent'] },
          category: { type: 'string' },
          isRead: { type: 'boolean' },
          createdAt: { type: 'string', format: 'date-time' }
        }
      },
      APIResponse: {
        type: 'object',
        properties: {
          success: { type: 'boolean' },
          data: { type: 'object' },
          message: { type: 'string' },
          timestamp: { type: 'string', format: 'date-time' }
        }
      },
      APIError: {
        type: 'object',
        properties: {
          success: { type: 'boolean', enum: [false] },
          error: {
            type: 'object',
            properties: {
              message: { type: 'string' },
              code: { type: 'string' },
              details: { type: 'object' },
              timestamp: { type: 'string', format: 'date-time' }
            }
          }
        }
      },
      PaginatedResponse: {
        type: 'object',
        properties: {
          success: { type: 'boolean' },
          data: { type: 'array', items: { type: 'object' } },
          pagination: {
            type: 'object',
            properties: {
              page: { type: 'integer' },
              limit: { type: 'integer' },
              total: { type: 'integer' },
              pages: { type: 'integer' }
            }
          },
          message: { type: 'string' },
          timestamp: { type: 'string', format: 'date-time' }
        }
      }
    }
  }

  private static generateTags(): any[] {
    const tagSet = new Set<string>()
    this.endpoints.forEach(endpoint => {
      endpoint.tags.forEach(tag => tagSet.add(tag))
    })

    return Array.from(tagSet).map(tag => ({
      name: tag,
      description: this.getTagDescription(tag)
    }))
  }

  private static getTagDescription(tag: string): string {
    const descriptions: Record<string, string> = {
      'Authentication': 'User authentication and authorization endpoints',
      'Quizzes': 'Quiz management and attempt endpoints',
      'Questions': 'Question management endpoints',
      'Notifications': 'Notification system endpoints',
      'Users': 'User management endpoints',
      'Files': 'File upload and management endpoints',
      'Analytics': 'Analytics and reporting endpoints',
      'Admin': 'Administrative endpoints',
      'Student': 'Student-specific endpoints'
    }
    return descriptions[tag] || `${tag} related endpoints`
  }
}

// Register all API endpoints
export function registerAPIEndpoints() {
  // Authentication endpoints
  APIDocumentation.register({
    path: '/api/auth/update-role',
    method: 'POST',
    summary: 'Update user role',
    description: 'Update the role of the authenticated user',
    tags: ['Authentication'],
    requiresAuth: true,
    requestBody: {
      required: true,
      contentType: 'application/json',
      schema: {
        type: 'object',
        properties: {
          role: { type: 'string', enum: ['STUDENT', 'ADMIN'] }
        },
        required: ['role']
      },
      example: { role: 'STUDENT' }
    },
    responses: [
      {
        status: 200,
        description: 'Role updated successfully',
        schema: { $ref: '#/components/schemas/APIResponse' },
        example: { success: true, message: 'Role updated successfully' }
      },
      {
        status: 401,
        description: 'Unauthorized',
        schema: { $ref: '#/components/schemas/APIError' }
      }
    ],
    rateLimit: { requests: 5, window: '15 minutes' }
  })

  // Quiz endpoints
  APIDocumentation.register({
    path: '/api/admin/quizzes',
    method: 'GET',
    summary: 'Get all quizzes',
    description: 'Retrieve a paginated list of all quizzes (Admin only)',
    tags: ['Quizzes', 'Admin'],
    requiresAuth: true,
    requiredRole: 'ADMIN',
    parameters: [
      {
        name: 'page',
        in: 'query',
        required: false,
        type: 'integer',
        description: 'Page number for pagination',
        example: 1
      },
      {
        name: 'limit',
        in: 'query',
        required: false,
        type: 'integer',
        description: 'Number of items per page',
        example: 10
      },
      {
        name: 'search',
        in: 'query',
        required: false,
        type: 'string',
        description: 'Search term for quiz title or description',
        example: 'javascript'
      }
    ],
    responses: [
      {
        status: 200,
        description: 'List of quizzes retrieved successfully',
        schema: { $ref: '#/components/schemas/PaginatedResponse' }
      }
    ],
    rateLimit: { requests: 100, window: '15 minutes' }
  })

  // Notification endpoints
  APIDocumentation.register({
    path: '/api/notifications',
    method: 'GET',
    summary: 'Get user notifications',
    description: 'Retrieve notifications for the authenticated user',
    tags: ['Notifications'],
    requiresAuth: true,
    parameters: [
      {
        name: 'page',
        in: 'query',
        required: false,
        type: 'integer',
        description: 'Page number',
        example: 1
      },
      {
        name: 'unreadOnly',
        in: 'query',
        required: false,
        type: 'boolean',
        description: 'Filter to unread notifications only',
        example: false
      }
    ],
    responses: [
      {
        status: 200,
        description: 'Notifications retrieved successfully',
        schema: { $ref: '#/components/schemas/PaginatedResponse' }
      }
    ],
    rateLimit: { requests: 1000, window: '15 minutes' }
  })

  // Add more endpoints...
}

// Generate markdown documentation
export function generateMarkdownDocs(): string {
  const endpoints = APIDocumentation.getAll()
  
  let markdown = `# Quiz Platform API Documentation

## Overview
This is the comprehensive API documentation for the Quiz Platform. The API provides endpoints for user authentication, quiz management, notifications, and more.

## Base URL
\`\`\`
${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api'}
\`\`\`

## Authentication
Most endpoints require authentication. The API uses session-based authentication with NextAuth.js.

## Rate Limiting
API endpoints are rate-limited to prevent abuse. Rate limits vary by endpoint and are documented for each endpoint.

## Response Format
All API responses follow a consistent format:

### Success Response
\`\`\`json
{
  "success": true,
  "data": {},
  "message": "Optional success message",
  "timestamp": "2024-01-25T10:30:00.000Z"
}
\`\`\`

### Error Response
\`\`\`json
{
  "success": false,
  "error": {
    "message": "Error description",
    "code": "ERROR_CODE",
    "details": {},
    "timestamp": "2024-01-25T10:30:00.000Z"
  }
}
\`\`\`

## Endpoints

`

  // Group endpoints by tag
  const groupedEndpoints = endpoints.reduce((acc, endpoint) => {
    endpoint.tags.forEach(tag => {
      if (!acc[tag]) acc[tag] = []
      acc[tag].push(endpoint)
    })
    return acc
  }, {} as Record<string, APIEndpoint[]>)

  Object.entries(groupedEndpoints).forEach(([tag, tagEndpoints]) => {
    markdown += `\n### ${tag}\n\n`
    
    tagEndpoints.forEach(endpoint => {
      markdown += `#### ${endpoint.method} ${endpoint.path}\n\n`
      markdown += `${endpoint.description}\n\n`
      
      if (endpoint.requiresAuth) {
        markdown += `**Authentication Required:** Yes\n`
        if (endpoint.requiredRole) {
          markdown += `**Required Role:** ${endpoint.requiredRole}\n`
        }
        markdown += `\n`
      }

      if (endpoint.rateLimit) {
        markdown += `**Rate Limit:** ${endpoint.rateLimit.requests} requests per ${endpoint.rateLimit.window}\n\n`
      }

      if (endpoint.parameters && endpoint.parameters.length > 0) {
        markdown += `**Parameters:**\n\n`
        endpoint.parameters.forEach(param => {
          markdown += `- \`${param.name}\` (${param.type}${param.required ? ', required' : ', optional'}): ${param.description}\n`
        })
        markdown += `\n`
      }

      if (endpoint.requestBody) {
        markdown += `**Request Body:**\n\n`
        markdown += `\`\`\`json\n${JSON.stringify(endpoint.requestBody.example, null, 2)}\n\`\`\`\n\n`
      }

      markdown += `**Responses:**\n\n`
      endpoint.responses.forEach(response => {
        markdown += `- \`${response.status}\`: ${response.description}\n`
      })
      markdown += `\n---\n\n`
    })
  })

  return markdown
}
