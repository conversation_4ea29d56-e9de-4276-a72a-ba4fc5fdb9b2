import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, rateLimits } from '@/lib/api-middleware'
import { AnalyticsService } from '@/lib/analytics-service'

// GET /api/analytics/realtime - Get real-time metrics
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 60 // 1 request per second
    }
  },
  async (request: NextRequest) => {
    const realTimeMetrics = await AnalyticsService.getRealTimeMetrics()
    
    return APIResponse.success(
      realTimeMetrics,
      'Real-time metrics retrieved successfully'
    )
  }
)
