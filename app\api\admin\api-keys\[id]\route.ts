import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, rateLimits } from '@/lib/api-middleware'
import { ApiKeyService } from '@/lib/api-key-service'

// GET /api/admin/api-keys/[id] - Get API key analytics
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.moderate
  },
  async (request: NextRequest, { params, user }) => {
    const apiKeyId = params.id

    try {
      const analytics = await ApiKeyService.getApiKeyAnalytics(apiKeyId, user.id)
      
      return APIResponse.success(
        analytics,
        'API key analytics retrieved successfully'
      )
    } catch (error) {
      if (error instanceof Error && error.message === 'API key not found') {
        return APIResponse.error(
          'API key not found',
          404,
          'API_KEY_NOT_FOUND'
        )
      }
      throw error
    }
  }
)

// PATCH /api/admin/api-keys/[id] - Revoke API key
export const PATCH = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.strict
  },
  async (request: NextRequest, { params, user }) => {
    const apiKeyId = params.id

    await ApiKeyService.revokeApiKey(apiKeyId, user.id)
    
    return APIResponse.success(
      null,
      'API key revoked successfully'
    )
  }
)

// DELETE /api/admin/api-keys/[id] - Delete API key
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.strict
  },
  async (request: NextRequest, { params, user }) => {
    const apiKeyId = params.id

    await ApiKeyService.deleteApiKey(apiKeyId, user.id)
    
    return APIResponse.success(
      null,
      'API key deleted successfully'
    )
  }
)
