import { NextRequest } from 'next/server'
import { z } from 'zod'
import { createAPIHandler, APIResponse, rateLimits } from '@/lib/api-middleware'
import { ApiKeyService, API_PERMISSIONS } from '@/lib/api-key-service'

const createApiKeySchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  permissions: z.array(z.string()).min(1, 'At least one permission required'),
  expiresAt: z.string().datetime().optional()
})

// GET /api/admin/api-keys - Get user's API keys
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.moderate
  },
  async (request: NextRequest, { user }) => {
    const apiKeys = await ApiKeyService.getUserApiKeys(user.id)
    
    return APIResponse.success(
      apiKeys,
      'API keys retrieved successfully'
    )
  }
)

// POST /api/admin/api-keys - Create new API key
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.strict,
    validateBody: createApiKeySchema
  },
  async (request: NextRequest, { user, validatedBody }) => {
    const { name, permissions, expiresAt } = validatedBody

    // Validate permissions
    const invalidPermissions = permissions.filter(
      permission => !(permission in API_PERMISSIONS)
    )

    if (invalidPermissions.length > 0) {
      return APIResponse.error(
        `Invalid permissions: ${invalidPermissions.join(', ')}`,
        400,
        'INVALID_PERMISSIONS'
      )
    }

    // Check if user already has too many API keys (limit to 10)
    const existingKeys = await ApiKeyService.getUserApiKeys(user.id)
    if (existingKeys.length >= 10) {
      return APIResponse.error(
        'Maximum number of API keys reached (10)',
        400,
        'API_KEY_LIMIT_EXCEEDED'
      )
    }

    const result = await ApiKeyService.createApiKey({
      name,
      permissions,
      expiresAt: expiresAt ? new Date(expiresAt) : undefined,
      userId: user.id
    })

    return APIResponse.success(
      {
        apiKey: result.apiKey,
        key: result.key // Only returned once during creation
      },
      'API key created successfully',
      201
    )
  }
)

// GET /api/admin/api-keys/permissions - Get available permissions
export const OPTIONS = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.lenient
  },
  async (request: NextRequest) => {
    const permissions = Object.entries(API_PERMISSIONS).map(([key, description]) => ({
      permission: key,
      description
    }))

    return APIResponse.success(
      { permissions },
      'Available permissions retrieved successfully'
    )
  }
)
