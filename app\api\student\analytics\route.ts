import { NextRequest } from 'next/server'
import { createAP<PERSON>Handler, APIResponse, rateLimits } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/student/analytics - Get student analytics data
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    rateLimit: rateLimits.moderate
  },
  async (request: NextRequest, { user }) => {
    try {
      // Get query parameters
      const { searchParams } = new URL(request.url)
      const timeRange = searchParams.get('timeRange') || '30' // days
      const days = parseInt(timeRange)
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      // Get all quiz attempts for the user
      const attempts = await prisma.quizAttempt.findMany({
        where: {
          userId: user.id,
          startedAt: { gte: startDate }
        },
        include: {
          quiz: {
            select: {
              title: true,
              type: true,
              difficulty: true,
              tags: true
            }
          }
        },
        orderBy: { startedAt: 'asc' }
      })

      // Calculate performance trends
      const performanceTrends = []
      const dailyStats = new Map()

      attempts.forEach(attempt => {
        const date = attempt.startedAt.toISOString().split('T')[0]
        
        if (!dailyStats.has(date)) {
          dailyStats.set(date, {
            date,
            attempts: 0,
            totalScore: 0,
            totalTime: 0,
            completedAttempts: 0
          })
        }

        const dayStats = dailyStats.get(date)
        dayStats.attempts++
        
        if (attempt.completedAt) {
          dayStats.completedAttempts++
          dayStats.totalScore += attempt.percentage || 0
          
          const timeSpent = attempt.completedAt.getTime() - attempt.startedAt.getTime()
          dayStats.totalTime += Math.round(timeSpent / (1000 * 60)) // minutes
        }
      })

      // Convert to array and calculate averages
      for (const [date, stats] of dailyStats) {
        performanceTrends.push({
          date,
          attempts: stats.attempts,
          averageScore: stats.completedAttempts > 0 ? Math.round(stats.totalScore / stats.completedAttempts) : 0,
          averageTime: stats.completedAttempts > 0 ? Math.round(stats.totalTime / stats.completedAttempts) : 0,
          completionRate: stats.attempts > 0 ? Math.round((stats.completedAttempts / stats.attempts) * 100) : 0
        })
      }

      // Subject performance analysis
      const subjectPerformance = new Map()
      
      attempts.forEach(attempt => {
        if (!attempt.completedAt) return

        const subjects = attempt.quiz.tags || ['General']
        subjects.forEach(subject => {
          if (!subjectPerformance.has(subject)) {
            subjectPerformance.set(subject, {
              subject,
              attempts: 0,
              totalScore: 0,
              bestScore: 0,
              totalTime: 0
            })
          }

          const subjectStats = subjectPerformance.get(subject)
          subjectStats.attempts++
          subjectStats.totalScore += attempt.percentage || 0
          subjectStats.bestScore = Math.max(subjectStats.bestScore, attempt.percentage || 0)
          
          const timeSpent = attempt.completedAt.getTime() - attempt.startedAt.getTime()
          subjectStats.totalTime += Math.round(timeSpent / (1000 * 60))
        })
      })

      const subjectAnalysis = Array.from(subjectPerformance.values()).map(stats => ({
        subject: stats.subject,
        attempts: stats.attempts,
        averageScore: Math.round(stats.totalScore / stats.attempts),
        bestScore: stats.bestScore,
        averageTime: Math.round(stats.totalTime / stats.attempts),
        improvement: 0 // Could calculate trend over time
      }))

      // Difficulty analysis
      const difficultyStats = new Map()
      
      attempts.forEach(attempt => {
        if (!attempt.completedAt) return

        const difficulty = attempt.quiz.difficulty
        if (!difficultyStats.has(difficulty)) {
          difficultyStats.set(difficulty, {
            difficulty,
            attempts: 0,
            totalScore: 0,
            completedAttempts: 0
          })
        }

        const stats = difficultyStats.get(difficulty)
        stats.attempts++
        stats.completedAttempts++
        stats.totalScore += attempt.percentage || 0
      })

      const difficultyAnalysis = Array.from(difficultyStats.values()).map(stats => ({
        difficulty: stats.difficulty,
        attempts: stats.attempts,
        averageScore: stats.completedAttempts > 0 ? Math.round(stats.totalScore / stats.completedAttempts) : 0,
        successRate: Math.round((stats.completedAttempts / stats.attempts) * 100)
      }))

      // Study patterns
      const hourlyActivity = new Array(24).fill(0)
      const weeklyActivity = new Array(7).fill(0)

      attempts.forEach(attempt => {
        const hour = attempt.startedAt.getHours()
        const dayOfWeek = attempt.startedAt.getDay()
        
        hourlyActivity[hour]++
        weeklyActivity[dayOfWeek]++
      })

      // Recent insights
      const completedAttempts = attempts.filter(a => a.completedAt)
      const recentScores = completedAttempts.slice(-10).map(a => a.percentage || 0)
      const averageRecentScore = recentScores.length > 0 ? 
        Math.round(recentScores.reduce((sum, score) => sum + score, 0) / recentScores.length) : 0

      const insights = []
      
      if (recentScores.length >= 5) {
        const trend = recentScores.slice(-5).reduce((sum, score, index) => {
          return sum + (score * (index + 1))
        }, 0) / 15 // weighted average
        
        if (trend > averageRecentScore + 5) {
          insights.push({
            type: 'positive',
            title: 'Improving Performance',
            description: 'Your recent quiz scores show an upward trend. Keep up the great work!'
          })
        } else if (trend < averageRecentScore - 5) {
          insights.push({
            type: 'warning',
            title: 'Performance Dip',
            description: 'Your recent scores have declined. Consider reviewing challenging topics.'
          })
        }
      }

      // Study time insights
      const totalStudyTime = completedAttempts.reduce((total, attempt) => {
        const timeSpent = attempt.completedAt!.getTime() - attempt.startedAt.getTime()
        return total + timeSpent
      }, 0)

      const averageStudyTime = completedAttempts.length > 0 ? 
        Math.round(totalStudyTime / (completedAttempts.length * 1000 * 60)) : 0

      if (averageStudyTime > 0) {
        if (averageStudyTime < 10) {
          insights.push({
            type: 'info',
            title: 'Quick Learner',
            description: `You complete quizzes efficiently with an average of ${averageStudyTime} minutes per quiz.`
          })
        } else if (averageStudyTime > 30) {
          insights.push({
            type: 'info',
            title: 'Thorough Approach',
            description: `You take your time with quizzes, averaging ${averageStudyTime} minutes. This thorough approach can lead to better understanding.`
          })
        }
      }

      // Get leaderboard position and streak data
      const leaderboardPosition = await getLeaderboardPosition(user.id)
      const streakData = await calculateStreakData(user.id)
      const totalStudents = await prisma.user.count({ where: { role: 'STUDENT' } })

      // Transform data to match frontend AnalyticsData interface exactly
      return APIResponse.success({
        overview: {
          totalAttempts: attempts.length,
          averageScore: averageRecentScore,
          totalTimeSpent: Math.round(totalStudyTime / (1000 * 60)), // minutes
          quizzesCompleted: completedAttempts.length,
          currentStreak: streakData.currentStreak,
          longestStreak: streakData.longestStreak,
          totalPoints: completedAttempts.reduce((sum, attempt) => sum + (attempt.score || 0), 0),
          rank: leaderboardPosition?.position || 0,
          totalStudents,
          improvementRate: 0 // TODO: Calculate trend from recent attempts
        },
        performanceByCategory: subjectAnalysis.map(subject => ({
          category: subject.subject,
          attempts: subject.attempts,
          averageScore: subject.averageScore,
          totalTime: subject.averageTime * subject.attempts,
          improvement: subject.improvement
        })),
        performanceByDifficulty: difficultyAnalysis,
        weeklyProgress: performanceTrends.slice(-4).map((trend, index) => ({
          week: `Week ${index + 1}`,
          attempts: trend.attempts,
          averageScore: trend.averageScore,
          timeSpent: trend.averageTime * trend.attempts
        })),
        strengths: subjectAnalysis
          .filter(subject => subject.averageScore >= 80)
          .map(subject => subject.subject)
          .slice(0, 4),
        weaknesses: subjectAnalysis
          .filter(subject => subject.averageScore < 70)
          .map(subject => subject.subject)
          .slice(0, 4),
        recommendations: insights.map(insight => insight.description).slice(0, 5),
        achievements: [
          {
            id: '1',
            title: 'First Steps',
            description: 'Complete your first quiz',
            icon: '🎯',
            unlockedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            rarity: 'common' as const
          },
          {
            id: '2',
            title: 'Week Warrior',
            description: 'Complete 7 quizzes in a week',
            icon: '🔥',
            unlockedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            rarity: 'rare' as const
          }
        ]
      }, 'Analytics data retrieved successfully')

    } catch (error) {
      console.error('Error fetching analytics:', error)
      return APIResponse.error('Failed to fetch analytics data', 500)
    }
  }
)

// Helper function to get leaderboard position
async function getLeaderboardPosition(userId: string) {
  // Get user's total points
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { totalPoints: true }
  })

  if (!user) return null

  // Count users with more points
  const usersAbove = await prisma.user.count({
    where: {
      totalPoints: { gt: user.totalPoints },
      role: 'STUDENT'
    }
  })

  return {
    position: usersAbove + 1,
    totalPoints: user.totalPoints
  }
}

// Helper function to calculate streak data
async function calculateStreakData(userId: string) {
  // Get all completed attempts ordered by completion date
  const attempts = await prisma.quizAttempt.findMany({
    where: {
      userId,
      isCompleted: true,
      completedAt: { not: null }
    },
    select: {
      completedAt: true
    },
    orderBy: { completedAt: 'desc' }
  })

  if (attempts.length === 0) {
    return { currentStreak: 0, longestStreak: 0 }
  }

  // Group attempts by date
  const attemptDates = attempts.map(attempt => {
    const date = new Date(attempt.completedAt!)
    return date.toDateString()
  })

  // Remove duplicates and sort
  const uniqueDates = [...new Set(attemptDates)].sort((a, b) => new Date(b).getTime() - new Date(a).getTime())

  // Calculate current streak
  let currentStreak = 0
  const today = new Date()

  for (let i = 0; i < uniqueDates.length; i++) {
    const attemptDate = new Date(uniqueDates[i])
    const daysDiff = Math.floor((today.getTime() - attemptDate.getTime()) / (1000 * 60 * 60 * 24))

    if (i === 0 && daysDiff <= 1) {
      currentStreak = 1
    } else if (i > 0) {
      const prevDate = new Date(uniqueDates[i - 1])
      const daysBetween = Math.floor((prevDate.getTime() - attemptDate.getTime()) / (1000 * 60 * 60 * 24))

      if (daysBetween === 1) {
        currentStreak++
      } else {
        break
      }
    } else {
      break
    }
  }

  // Calculate longest streak
  let longestStreak = 0
  let tempStreak = 1

  for (let i = 1; i < uniqueDates.length; i++) {
    const currentDate = new Date(uniqueDates[i])
    const prevDate = new Date(uniqueDates[i - 1])
    const daysBetween = Math.floor((prevDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24))

    if (daysBetween === 1) {
      tempStreak++
    } else {
      longestStreak = Math.max(longestStreak, tempStreak)
      tempStreak = 1
    }
  }

  longestStreak = Math.max(longestStreak, tempStreak)

  return { currentStreak, longestStreak }
}
