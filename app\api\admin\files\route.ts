import { NextRequest } from 'next/server'
import { z } from 'zod'
import { createAPIHandler, APIResponse, rateLimits } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const querySchema = z.object({
  page: z.string().transform(val => parseInt(val) || 1),
  limit: z.string().transform(val => Math.min(parseInt(val) || 20, 100)),
  uploadType: z.string().optional(),
  folder: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['name', 'size', 'date', 'type']).optional().default('date'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc')
})

const uploadFileSchema = z.object({
  filename: z.string().min(1, "Filename is required"),
  originalName: z.string().min(1, "Original name is required"),
  mimeType: z.string().min(1, "MIME type is required"),
  size: z.number().min(1, "File size must be greater than 0"),
  url: z.string().url("Valid URL is required"),
  uploadType: z.enum(['image', 'document', 'general']).default('general'),
  folder: z.string().default('uploads')
})

// GET /api/admin/files - Get files with pagination and filtering
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.moderate,
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    const { page, limit, uploadType, folder, search, sortBy, sortOrder } = validatedQuery
    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { originalName: { contains: search, mode: 'insensitive' } },
        { filename: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (uploadType) {
      where.uploadType = uploadType
    }

    if (folder) {
      where.folder = folder
    }

    // Build orderBy clause
    let orderBy: any = {}
    switch (sortBy) {
      case 'name':
        orderBy = { originalName: sortOrder }
        break
      case 'size':
        orderBy = { size: sortOrder }
        break
      case 'type':
        orderBy = { mimeType: sortOrder }
        break
      case 'date':
      default:
        orderBy = { createdAt: sortOrder }
        break
    }

    const [files, totalCount] = await Promise.all([
      prisma.file.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          uploadedBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      }),
      prisma.file.count({ where })
    ])

    // Process files with formatted data
    const filesWithFormatting = files.map(file => ({
      id: file.id,
      filename: file.filename,
      originalName: file.originalName,
      mimeType: file.mimeType,
      size: file.size,
      url: file.url,
      uploadType: file.uploadType,
      folder: file.folder,
      createdAt: file.createdAt.toISOString(),
      updatedAt: file.updatedAt.toISOString(),
      uploadedBy: file.uploadedBy,
      formattedSize: formatFileSize(file.size),
      isImage: file.mimeType.startsWith('image/'),
      isDocument: file.mimeType.includes('pdf') || file.mimeType.includes('document') || file.mimeType.includes('text')
    }))

    const totalPages = Math.ceil(totalCount / limit)

    return APIResponse.success({
      files: filesWithFormatting,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }, 'Files retrieved successfully')
  }
)

// POST /api/admin/files - Upload new file
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.strict,
    validateBody: uploadFileSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    const { filename, originalName, mimeType, size, url, uploadType, folder } = validatedBody

    try {
      // Check if file already exists
      const existingFile = await prisma.file.findFirst({
        where: {
          filename,
          folder
        }
      })

      if (existingFile) {
        return APIResponse.error(
          'A file with this name already exists in the specified folder',
          400,
          'FILE_ALREADY_EXISTS'
        )
      }

      // Create the file record
      const file = await prisma.file.create({
        data: {
          filename,
          originalName,
          mimeType,
          size,
          url,
          uploadType,
          folder,
          uploadedById: user.id
        },
        include: {
          uploadedBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })

      return APIResponse.success(
        {
          id: file.id,
          filename: file.filename,
          originalName: file.originalName,
          mimeType: file.mimeType,
          size: file.size,
          url: file.url,
          uploadType: file.uploadType,
          folder: file.folder,
          createdAt: file.createdAt.toISOString(),
          uploadedBy: file.uploadedBy,
          formattedSize: formatFileSize(file.size),
          isImage: file.mimeType.startsWith('image/'),
          isDocument: file.mimeType.includes('pdf') || file.mimeType.includes('document') || file.mimeType.includes('text')
        },
        'File uploaded successfully',
        201
      )
    } catch (error) {
      console.error('Error uploading file:', error)
      return APIResponse.error('Failed to upload file', 500)
    }
  }
)

// DELETE /api/admin/files - Bulk delete files
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    rateLimit: rateLimits.strict,
    validateBody: z.object({
      fileIds: z.array(z.string()).min(1, "At least one file ID is required")
    })
  },
  async (request: NextRequest, { validatedBody }) => {
    const { fileIds } = validatedBody

    try {
      // Get files to be deleted for cleanup
      const filesToDelete = await prisma.file.findMany({
        where: {
          id: { in: fileIds }
        },
        select: {
          id: true,
          filename: true,
          url: true,
          folder: true
        }
      })

      // Delete from database
      const deletedFiles = await prisma.file.deleteMany({
        where: {
          id: { in: fileIds }
        }
      })

      // In a real implementation, you would also delete the actual files from storage
      // For now, we'll just log what would be deleted
      console.log('Files to be deleted from storage:', filesToDelete.map(f => f.url))

      return APIResponse.success(
        { 
          deletedCount: deletedFiles.count,
          deletedFiles: filesToDelete.map(f => ({ id: f.id, filename: f.filename }))
        },
        `Successfully deleted ${deletedFiles.count} file(s)`
      )
    } catch (error) {
      console.error('Error deleting files:', error)
      return APIResponse.error('Failed to delete files', 500)
    }
  }
)

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
