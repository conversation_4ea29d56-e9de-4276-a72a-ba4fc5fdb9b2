"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { 
  FileText, 
  Download, 
  Award, 
  BarChart3, 
  Settings,
  Eye,
  Users,
  TrendingUp,
  Clock,
  Target,
  CheckCircle,
  XCircle,
  Trash2,
  HardDrive
} from "lucide-react"
import { PDFExportButton, QuizResultExportButton, AnalyticsExportButton, CertificateExportButton, PDFExportStatus } from "@/components/pdf/pdf-export-button"
import { PDFPreview, QuizResultPreview, AnalyticsPreview, CertificatePreview, PDFTemplateSelector, PDFExportQueue } from "@/components/pdf/pdf-preview"
import { toast } from "sonner"

interface PdfExport {
  id: string
  type: 'quiz-result' | 'analytics' | 'certificate' | 'bulk'
  filename: string
  size: number
  status: 'pending' | 'processing' | 'completed' | 'failed'
  options: any
  error?: string
  createdAt: string
  updatedAt: string
  user: {
    id: string
    name: string
    email: string
  }
}

interface PdfExportStats {
  overview: {
    totalExports: number
    completedExports: number
    failedExports: number
    pendingExports: number
    successRate: number
    totalSize: string
    totalSizeBytes: number
  }
  exportsByType: Array<{
    type: string
    count: number
  }>
  recentExports: Array<{
    id: string
    type: string
    filename: string
    size: number
    status: string
    createdAt: string
    user: {
      id: string
      name: string
      email: string
    }
  }>
  period: string
}

// Sample data structure for PDF exports (will be replaced with real data from API)
const sampleQuizResult = {
  id: "quiz-result-sample",
  quiz: {
    title: "Sample Quiz Assessment",
    description: "This is a sample quiz result for demonstration purposes",
    category: "Sample",
    difficulty: "MEDIUM",
    totalQuestions: 10,
    totalPoints: 100,
    duration: 30
  },
  student: {
    name: "Sample Student",
    email: "<EMAIL>",
    id: "sample-student"
  },
  score: 87,
  percentage: 87,
  timeSpent: 38,
  completedAt: "2024-01-25T14:30:00Z",
  questions: [
    {
      id: "q1",
      text: "What is the difference between let and var in JavaScript?",
      type: "multiple-choice",
      options: ["Scope", "Hoisting", "Both A and B", "None"],
      correctAnswer: "Both A and B",
      studentAnswer: "Both A and B",
      isCorrect: true,
      points: 4,
      pointsEarned: 4,
      explanation: "let has block scope and is not hoisted, while var has function scope and is hoisted."
    },
    {
      id: "q2",
      text: "Which method is used to add an element to the end of an array?",
      type: "multiple-choice",
      options: ["push()", "pop()", "shift()", "unshift()"],
      correctAnswer: "push()",
      studentAnswer: "pop()",
      isCorrect: false,
      points: 4,
      pointsEarned: 0,
      explanation: "push() adds elements to the end, pop() removes from the end."
    }
    // Add more questions as needed
  ],
  rank: 3,
  totalAttempts: 1
}

const mockAnalyticsData = {
  analytics: {
    overview: {
      totalAttempts: 47,
      averageScore: 78.5,
      totalTimeSpent: 1240,
      improvementRate: 15.2
    },
    performanceByCategory: [
      {
        category: "Programming",
        attempts: 18,
        averageScore: 82.3,
        improvement: 12.5
      },
      {
        category: "Web Development",
        attempts: 15,
        averageScore: 76.8,
        improvement: 18.7
      },
      {
        category: "Data Science",
        attempts: 8,
        averageScore: 71.2,
        improvement: 8.3
      }
    ],
    performanceByDifficulty: [
      {
        difficulty: "EASY",
        attempts: 20,
        averageScore: 89.2,
        successRate: 95
      },
      {
        difficulty: "MEDIUM",
        attempts: 22,
        averageScore: 75.8,
        successRate: 82
      },
      {
        difficulty: "HARD",
        attempts: 5,
        averageScore: 62.4,
        successRate: 60
      }
    ],
    weeklyProgress: [
      { week: "Week 1", attempts: 8, averageScore: 72.5, timeSpent: 180 },
      { week: "Week 2", attempts: 12, averageScore: 76.3, timeSpent: 280 },
      { week: "Week 3", attempts: 15, averageScore: 79.8, timeSpent: 350 },
      { week: "Week 4", attempts: 12, averageScore: 81.2, timeSpent: 430 }
    ]
  },
  studentName: "John Doe"
}

const mockCertificateData = {
  studentName: "John Doe",
  quizTitle: "JavaScript Fundamentals Assessment",
  score: 87,
  completedAt: "2024-01-25T14:30:00Z",
  certificateId: "CERT-JS-2024-001"
}

const mockExports = [
  {
    id: "1",
    type: "quiz-result",
    filename: "quiz-result-john-doe-2024-01-25.pdf",
    status: "completed" as const,
    createdAt: new Date("2024-01-25T15:00:00Z"),
    downloadUrl: "#"
  },
  {
    id: "2",
    type: "analytics",
    filename: "analytics-john-doe-2024-01-25.pdf",
    status: "processing" as const,
    createdAt: new Date("2024-01-25T15:05:00Z")
  },
  {
    id: "3",
    type: "certificate",
    filename: "certificate-john-doe-2024-01-25.pdf",
    status: "failed" as const,
    createdAt: new Date("2024-01-25T15:10:00Z")
  }
]

const mockQueue = [
  {
    id: "1",
    type: "quiz-result",
    status: "processing" as const,
    progress: 75,
    filename: "quiz-result-jane-smith.pdf"
  },
  {
    id: "2",
    type: "analytics",
    status: "pending" as const,
    progress: 0,
    filename: "analytics-report-march.pdf"
  }
]

export default function PDFExportsPage() {
  const [selectedTemplate, setSelectedTemplate] = useState("modern")
  const [activePreview, setActivePreview] = useState<string | null>(null)
  const [exports, setExports] = useState<PdfExport[]>([])
  const [stats, setStats] = useState<PdfExportStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [availableQuizzes, setAvailableQuizzes] = useState<any[]>([])
  const [availableStudents, setAvailableStudents] = useState<any[]>([])
  const [refreshing, setRefreshing] = useState(false)
  const [period, setPeriod] = useState<'7d' | '30d' | '90d' | 'all'>('30d')

  // Fetch real data for dropdowns
  const fetchAvailableData = async () => {
    try {
      const [quizzesRes, studentsRes] = await Promise.all([
        fetch('/api/admin/quizzes?published=true'),
        fetch('/api/admin/users?role=STUDENT&limit=100')
      ])

      if (quizzesRes.ok) {
        const quizzesData = await quizzesRes.json()
        setAvailableQuizzes(quizzesData.data?.quizzes || [])
      }

      if (studentsRes.ok) {
        const studentsData = await studentsRes.json()
        setAvailableStudents(studentsData.data?.users || [])
      }
    } catch (error) {
      console.error('Error fetching available data:', error)
    }
  }
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'processing' | 'completed' | 'failed'>('all')
  const [typeFilter, setTypeFilter] = useState<string>('')

  useEffect(() => {
    fetchExports()
    fetchStats()
    fetchAvailableData()
  }, [currentPage, statusFilter, typeFilter, period])

  const fetchExports = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        status: statusFilter,
        ...(typeFilter && { type: typeFilter })
      })

      const response = await fetch(`/api/admin/pdf-exports?${params}`)
      if (!response.ok) throw new Error('Failed to fetch exports')

      const data = await response.json()
      setExports(data.data.exports)
      setTotalPages(data.data.pagination.totalPages)
    } catch (error) {
      console.error('Error fetching exports:', error)
      toast.error('Failed to load PDF exports')
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await fetch(`/api/admin/pdf-exports/stats?period=${period}`)
      if (!response.ok) throw new Error('Failed to fetch stats')

      const data = await response.json()
      setStats(data.data)
    } catch (error) {
      console.error('Error fetching stats:', error)
      toast.error('Failed to load statistics')
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await Promise.all([fetchExports(), fetchStats()])
    setRefreshing(false)
    toast.success('Data refreshed')
  }

  const handleCreateExport = async (type: string, options: any) => {
    try {
      console.log('Creating export:', { type, options })

      const response = await fetch('/api/admin/pdf-exports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type,
          filename: `${type}-${Date.now()}.pdf`,
          options
        })
      })

      if (!response.ok) {
        const error = await response.json()
        console.error('Export creation failed:', error)
        throw new Error(error.error?.message || 'Failed to create export')
      }

      const data = await response.json()
      console.log('Export created successfully:', data.data)

      setExports(prev => [data.data, ...prev])
      toast.success(`${type.charAt(0).toUpperCase() + type.slice(1)} export created successfully! Processing in background...`)

      // Refresh the exports list after a short delay to show updated status
      setTimeout(() => {
        fetchExports()
      }, 3000)

    } catch (error: any) {
      console.error('Error creating export:', error)
      toast.error(error.message || 'Failed to create export')
    }
  }

  const handleDeleteExport = async (exportId: string) => {
    if (!confirm('Are you sure you want to delete this export?')) {
      return
    }

    try {
      const response = await fetch(`/api/admin/pdf-exports/${exportId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || 'Failed to delete export')
      }

      setExports(prev => prev.filter(exp => exp.id !== exportId))
      toast.success('Export deleted successfully')
    } catch (error: any) {
      console.error('Error deleting export:', error)
      toast.error(error.message || 'Failed to delete export')
    }
  }

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <FileText className="h-8 w-8 text-blue-600" />
            PDF Export System
          </h1>
          <p className="text-muted-foreground mt-1">
            Generate and manage PDF exports for quiz results, analytics, and certificates
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value as '7d' | '30d' | '90d' | 'all')}
            className="px-3 py-1 border rounded-md text-sm"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="all">All time</option>
          </select>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <Download className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                  ) : (
                    stats?.overview.totalExports.toLocaleString() || '0'
                  )}
                </div>
                <p className="text-sm text-muted-foreground">Total Exports</p>
              </div>
              <Download className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                  ) : (
                    stats?.overview.completedExports.toLocaleString() || '0'
                  )}
                </div>
                <p className="text-sm text-muted-foreground">Completed</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                  ) : (
                    stats?.overview.pendingExports.toLocaleString() || '0'
                  )}
                </div>
                <p className="text-sm text-muted-foreground">In Queue</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                  ) : (
                    `${stats?.overview.successRate || 0}%`
                  )}
                </div>
                <p className="text-sm text-muted-foreground">Success Rate</p>
              </div>
              <Target className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="exports" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="exports">Export Tools</TabsTrigger>
          <TabsTrigger value="previews">Previews</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        {/* Export Tools Tab */}
        <TabsContent value="exports" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Quiz Result Export */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  Quiz Result Export
                </CardTitle>
                <CardDescription>
                  Generate detailed quiz result reports with performance analysis
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <h4 className="font-medium mb-2">Sample Quiz Result</h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p>Student: {sampleQuizResult.student.name}</p>
                    <p>Quiz: {sampleQuizResult.quiz.title}</p>
                    <p>Score: {sampleQuizResult.percentage}%</p>
                    <p>Questions: {sampleQuizResult.quiz.totalQuestions}</p>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="default"
                    size="sm"
                    className="flex-1"
                    onClick={() => handleCreateExport('quiz-result', {
                      quizId: 'quiz-1',
                      userId: sampleQuizResult.student.id,
                      includeAnswers: true,
                      includeStatistics: true
                    })}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export PDF
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setActivePreview('quiz-result')}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Analytics Export */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-green-600" />
                  Analytics Export
                </CardTitle>
                <CardDescription>
                  Export comprehensive learning analytics and performance reports
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <h4 className="font-medium mb-2">Sample Analytics</h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p>Student: {mockAnalyticsData.studentName}</p>
                    <p>Attempts: {mockAnalyticsData.analytics.overview.totalAttempts}</p>
                    <p>Avg Score: {mockAnalyticsData.analytics.overview.averageScore}%</p>
                    <p>Improvement: +{mockAnalyticsData.analytics.overview.improvementRate}%</p>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="default"
                    size="sm"
                    className="flex-1"
                    onClick={() => handleCreateExport('analytics', {
                      dateRange: {
                        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
                        end: new Date().toISOString()
                      },
                      includeStatistics: true
                    })}
                  >
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Export Analytics
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setActivePreview('analytics')}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Certificate Export */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-yellow-600" />
                  Certificate Export
                </CardTitle>
                <CardDescription>
                  Generate professional certificates for completed quizzes
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <h4 className="font-medium mb-2">Sample Certificate</h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p>Student: {mockCertificateData.studentName}</p>
                    <p>Quiz: {mockCertificateData.quizTitle}</p>
                    <p>Score: {mockCertificateData.score}%</p>
                    <p>ID: {mockCertificateData.certificateId}</p>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <CertificateExportButton 
                    certificateData={mockCertificateData}
                    variant="default"
                    size="sm"
                    className="flex-1"
                  />
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setActivePreview('certificate')}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Bulk Export Tools */}
          <Card>
            <CardHeader>
              <CardTitle>Bulk Export Tools</CardTitle>
              <CardDescription>
                Export multiple documents at once
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button variant="outline" className="h-20 flex-col">
                  <Users className="h-6 w-6 mb-2" />
                  <span>Export All Students</span>
                </Button>
                
                <Button variant="outline" className="h-20 flex-col">
                  <FileText className="h-6 w-6 mb-2" />
                  <span>Export Quiz Results</span>
                </Button>
                
                <Button variant="outline" className="h-20 flex-col">
                  <Award className="h-6 w-6 mb-2" />
                  <span>Export Certificates</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Previews Tab */}
        <TabsContent value="previews" className="space-y-6">
          {activePreview === 'quiz-result' && (
            <QuizResultPreview result={sampleQuizResult} />
          )}
          
          {activePreview === 'analytics' && (
            <AnalyticsPreview 
              analytics={mockAnalyticsData.analytics}
              studentName={mockAnalyticsData.studentName}
            />
          )}
          
          {activePreview === 'certificate' && (
            <CertificatePreview certificateData={mockCertificateData} />
          )}
          
          {!activePreview && (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <Eye className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-2">No Preview Selected</h3>
                  <p className="text-muted-foreground mb-6">
                    Select a document type from the Export Tools tab to see a preview
                  </p>
                  <div className="flex justify-center gap-2">
                    <Button 
                      variant="outline" 
                      onClick={() => setActivePreview('quiz-result')}
                    >
                      Quiz Result
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setActivePreview('analytics')}
                    >
                      Analytics
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setActivePreview('certificate')}
                    >
                      Certificate
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Templates Tab */}
        <TabsContent value="templates" className="space-y-6">
          <PDFTemplateSelector onTemplateSelect={setSelectedTemplate} />
          
          <Card>
            <CardHeader>
              <CardTitle>Template Settings</CardTitle>
              <CardDescription>
                Customize your PDF templates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Selected Template</label>
                  <div className="mt-1">
                    <Badge variant="outline">{selectedTemplate}</Badge>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Header Color</label>
                    <div className="mt-1 flex gap-2">
                      <div className="w-8 h-8 bg-blue-600 rounded border cursor-pointer"></div>
                      <div className="w-8 h-8 bg-green-600 rounded border cursor-pointer"></div>
                      <div className="w-8 h-8 bg-purple-600 rounded border cursor-pointer"></div>
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium">Font Size</label>
                    <div className="mt-1">
                      <select className="w-full p-2 border rounded">
                        <option>Small</option>
                        <option>Medium</option>
                        <option>Large</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history" className="space-y-6">
          <div className="space-y-6">
            {/* Filters */}
            <div className="flex items-center gap-4">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="processing">Processing</option>
                <option value="completed">Completed</option>
                <option value="failed">Failed</option>
              </select>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="">All Types</option>
                <option value="quiz-result">Quiz Results</option>
                <option value="analytics">Analytics</option>
                <option value="certificate">Certificates</option>
                <option value="bulk">Bulk Exports</option>
              </select>
            </div>

            {/* Export List */}
            <Card>
              <CardHeader>
                <CardTitle>Export History</CardTitle>
                <CardDescription>
                  Recent PDF exports and their status
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex items-center justify-between p-4 border rounded-lg animate-pulse">
                        <div className="flex items-center gap-4">
                          <div className="w-8 h-8 bg-muted rounded"></div>
                          <div className="flex-1">
                            <div className="h-4 bg-muted rounded w-48 mb-2"></div>
                            <div className="h-3 bg-muted rounded w-32"></div>
                          </div>
                        </div>
                        <div className="h-6 w-20 bg-muted rounded"></div>
                      </div>
                    ))}
                  </div>
                ) : exports.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No exports found</h3>
                    <p className="text-muted-foreground">
                      No PDF exports match your current filters.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {exports.map((exportItem) => (
                      <div key={exportItem.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-4">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            exportItem.status === 'completed' ? 'bg-green-100 text-green-600' :
                            exportItem.status === 'failed' ? 'bg-red-100 text-red-600' :
                            exportItem.status === 'processing' ? 'bg-blue-100 text-blue-600' :
                            'bg-gray-100 text-gray-600'
                          }`}>
                            {exportItem.status === 'completed' && <CheckCircle className="h-4 w-4" />}
                            {exportItem.status === 'failed' && <XCircle className="h-4 w-4" />}
                            {exportItem.status === 'processing' && <Clock className="h-4 w-4" />}
                            {exportItem.status === 'pending' && <Clock className="h-4 w-4" />}
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium">{exportItem.filename}</h4>
                            <p className="text-sm text-muted-foreground">
                              {exportItem.type} • {exportItem.user.name} • {new Date(exportItem.createdAt).toLocaleDateString()}
                            </p>
                            {exportItem.size > 0 && (
                              <p className="text-xs text-muted-foreground">
                                {(exportItem.size / 1024 / 1024).toFixed(2)} MB
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {exportItem.status === 'completed' && (
                            <Button variant="outline" size="sm">
                              <Download className="h-4 w-4 mr-2" />
                              Download
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteExport(exportItem.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Pagination */}
                {!loading && exports.length > 0 && totalPages > 1 && (
                  <div className="flex items-center justify-center gap-2 mt-6">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <span className="text-sm text-muted-foreground">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
