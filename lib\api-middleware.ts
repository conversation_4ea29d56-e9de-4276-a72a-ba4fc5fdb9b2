import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { z } from 'zod'
import { ApiKeyService, ApiKeyInfo } from '@/lib/api-key-service'

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  keyGenerator?: (request: NextRequest) => string // Custom key generator
}

export interface APIMiddlewareConfig {
  requireAuth?: boolean
  requireRole?: 'ADMIN' | 'STUDENT'
  allowApiKey?: boolean
  requiredPermission?: string
  rateLimit?: RateLimitConfig
  validateBody?: z.ZodSchema
  validateQuery?: z.ZodSchema
}

export class APIError extends Error {
  constructor(
    public message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message)
    this.name = 'APIError'
  }
}

export class APIResponse {
  static success(data: any, message?: string, statusCode: number = 200) {
    return NextResponse.json({
      success: true,
      data,
      message,
      timestamp: new Date().toISOString()
    }, { status: statusCode })
  }

  static error(
    message: string, 
    statusCode: number = 500, 
    code?: string, 
    details?: any
  ) {
    return NextResponse.json({
      success: false,
      error: {
        message,
        code,
        details,
        timestamp: new Date().toISOString()
      }
    }, { status: statusCode })
  }

  static paginated(
    data: any[],
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
    },
    message?: string
  ) {
    return NextResponse.json({
      success: true,
      data,
      pagination,
      message,
      timestamp: new Date().toISOString()
    })
  }

  static unauthorized(message: string = 'Unauthorized') {
    return this.error(message, 401, 'UNAUTHORIZED')
  }

  static forbidden(message: string = 'Forbidden') {
    return this.error(message, 403, 'FORBIDDEN')
  }

  static notFound(message: string = 'Not found') {
    return this.error(message, 404, 'NOT_FOUND')
  }

  static badRequest(message: string = 'Bad request') {
    return this.error(message, 400, 'BAD_REQUEST')
  }
}

export function createAPIHandler(
  config: APIMiddlewareConfig,
  handler: (request: NextRequest, context: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context?: any) => {
    try {
      // Rate limiting
      if (config.rateLimit) {
        const rateLimitResult = await applyRateLimit(request, config.rateLimit)
        if (!rateLimitResult.allowed) {
          return APIResponse.error(
            'Rate limit exceeded',
            429,
            'RATE_LIMIT_EXCEEDED',
            {
              limit: config.rateLimit.maxRequests,
              windowMs: config.rateLimit.windowMs,
              resetTime: rateLimitResult.resetTime
            }
          )
        }
      }

      // Authentication
      let session = null
      let apiKey: ApiKeyInfo | null = null
      let authType: 'session' | 'api_key' | null = null

      if (config.requireAuth || config.requireRole || config.allowApiKey) {
        // Try API key authentication first
        if (config.allowApiKey) {
          const authHeader = request.headers.get('authorization')
          if (authHeader?.startsWith('Bearer ')) {
            const key = authHeader.substring(7)
            apiKey = await ApiKeyService.validateApiKey(key)
            if (apiKey) {
              authType = 'api_key'

              // Check required permission for API key
              if (config.requiredPermission && !ApiKeyService.hasPermission(apiKey, config.requiredPermission)) {
                return APIResponse.error(
                  `Permission '${config.requiredPermission}' required`,
                  403,
                  'INSUFFICIENT_PERMISSIONS'
                )
              }

              // Log API usage
              const startTime = Date.now()
              request.headers.set('x-api-start-time', startTime.toString())
              request.headers.set('x-api-key-id', apiKey.id)
            }
          }
        }

        // Fall back to session authentication if no valid API key
        if (!apiKey && (config.requireAuth || config.requireRole)) {
          session = await auth()
          if (!session?.user?.id) {
            return APIResponse.error('Authentication required', 401, 'UNAUTHORIZED')
          }
          authType = 'session'

          // Role-based access control for session auth
          if (config.requireRole && session.user.role !== config.requireRole) {
            return APIResponse.error(
              `${config.requireRole} role required`,
              403,
              'FORBIDDEN'
            )
          }
        }

        // If authentication is required but neither session nor API key is valid
        if ((config.requireAuth || config.requireRole) && !session && !apiKey) {
          return APIResponse.error('Authentication required', 401, 'UNAUTHORIZED')
        }
      }

      // Request body validation
      let validatedBody = null
      if (config.validateBody && ['POST', 'PUT', 'PATCH'].includes(request.method)) {
        try {
          const body = await request.json()
          validatedBody = config.validateBody.parse(body)
        } catch (error) {
          if (error instanceof z.ZodError) {
            return APIResponse.error(
              'Invalid request body',
              400,
              'VALIDATION_ERROR',
              error.errors
            )
          }
          return APIResponse.error('Invalid JSON in request body', 400, 'INVALID_JSON')
        }
      }

      // Query parameters validation
      let validatedQuery = null
      if (config.validateQuery) {
        try {
          const { searchParams } = new URL(request.url)
          const queryObject = Object.fromEntries(searchParams.entries())
          validatedQuery = config.validateQuery.parse(queryObject)
        } catch (error) {
          if (error instanceof z.ZodError) {
            return APIResponse.error(
              'Invalid query parameters',
              400,
              'VALIDATION_ERROR',
              error.errors
            )
          }
        }
      }

      // Create enhanced context
      const enhancedContext = {
        ...context,
        session,
        apiKey,
        authType,
        validatedBody,
        validatedQuery,
        user: session?.user
      }

      // Call the actual handler
      const response = await handler(request, enhancedContext)

      // Log API usage if using API key
      if (apiKey) {
        const startTime = parseInt(request.headers.get('x-api-start-time') || '0')
        const responseTime = startTime ? Date.now() - startTime : 0
        const ip = request.headers.get('x-forwarded-for')?.split(',')[0] ||
                  request.headers.get('x-real-ip') || 'unknown'

        // Don't await this to avoid slowing down the response
        ApiKeyService.logApiUsage({
          apiKeyId: apiKey.id,
          endpoint: new URL(request.url).pathname,
          method: request.method,
          statusCode: response.status,
          responseTime,
          ipAddress: ip,
          userAgent: request.headers.get('user-agent') || undefined
        }).catch(console.error)
      }

      return response

    } catch (error) {
      console.error('API Handler Error:', error)

      if (error instanceof APIError) {
        return APIResponse.error(error.message, error.statusCode, error.code)
      }

      if (error instanceof z.ZodError) {
        return APIResponse.error(
          'Validation error',
          400,
          'VALIDATION_ERROR',
          error.errors
        )
      }

      // Generic error
      return APIResponse.error(
        'Internal server error',
        500,
        'INTERNAL_ERROR'
      )
    }
  }
}

async function applyRateLimit(
  request: NextRequest,
  config: RateLimitConfig
): Promise<{ allowed: boolean; resetTime?: number }> {
  const key = config.keyGenerator 
    ? config.keyGenerator(request)
    : getDefaultRateLimitKey(request)

  const now = Date.now()

  // Clean up old entries
  for (const [k, v] of rateLimitStore.entries()) {
    if (v.resetTime < now) {
      rateLimitStore.delete(k)
    }
  }

  const current = rateLimitStore.get(key)
  
  if (!current || current.resetTime < now) {
    // First request in window or window expired
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + config.windowMs
    })
    return { allowed: true }
  }

  if (current.count >= config.maxRequests) {
    return { 
      allowed: false, 
      resetTime: current.resetTime 
    }
  }

  // Increment count
  current.count++
  rateLimitStore.set(key, current)
  
  return { allowed: true }
}

function getDefaultRateLimitKey(request: NextRequest): string {
  // Use IP address as default key
  const forwarded = request.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0] : 
             request.headers.get('x-real-ip') || 
             'unknown'
  
  return `rate_limit:${ip}`
}

// Common validation schemas
export const commonSchemas = {
  pagination: z.object({
    page: z.string().transform(val => parseInt(val) || 1),
    limit: z.string().transform(val => Math.min(parseInt(val) || 20, 100)),
    sort: z.string().optional(),
    order: z.enum(['asc', 'desc']).optional()
  }),

  search: z.object({
    q: z.string().optional(),
    category: z.string().optional(),
    tags: z.string().optional(),
    status: z.string().optional()
  }),

  idParam: z.object({
    id: z.string().min(1, 'ID is required')
  })
}

// Common rate limit configurations
export const rateLimits = {
  // Very restrictive for sensitive operations
  strict: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5
  },

  // Moderate for general API usage
  moderate: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100
  },

  // Lenient for read operations
  lenient: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 1000
  },

  // For file uploads
  upload: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 50
  }
}

// API versioning helper
export function getAPIVersion(request: NextRequest): string {
  const version = request.headers.get('api-version') || 
                 request.nextUrl.searchParams.get('version') ||
                 'v1'
  return version
}

// CORS helper
export function setCORSHeaders(response: NextResponse, origin?: string): NextResponse {
  if (origin) {
    response.headers.set('Access-Control-Allow-Origin', origin)
  }
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, API-Version')
  response.headers.set('Access-Control-Max-Age', '86400')
  
  return response
}

// Request logging
export function logAPIRequest(request: NextRequest, response?: NextResponse) {
  const timestamp = new Date().toISOString()
  const method = request.method
  const url = request.url
  const userAgent = request.headers.get('user-agent') || 'unknown'
  const ip = request.headers.get('x-forwarded-for') || 'unknown'
  const status = response?.status || 'pending'

  console.log(`[${timestamp}] ${method} ${url} - ${status} - ${ip} - ${userAgent}`)
}

// Health check endpoint helper
export function createHealthCheck() {
  return APIResponse.success({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  })
}
